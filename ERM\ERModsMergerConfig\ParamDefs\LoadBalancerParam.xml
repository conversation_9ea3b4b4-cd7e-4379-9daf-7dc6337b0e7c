﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>LOAD_BALANCER_PARAM_ST</ParamType>
  <DataVersion>0</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 lowerFpsThreshold = 23">
      <Description>このFPSを下回ったら、ロードバランスレベルを1上げる</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>60</Maximum>
      <Increment>0.1</Increment>
      <UnkC8>基本設定</UnkC8>
    </Field>
    <Field Def="f32 upperFpsThreshold = 27">
      <Description>このFPSを上回ったら、ロードバランスレベルを1下げる</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>60</Maximum>
      <Increment>0.1</Increment>
      <SortID>1</SortID>
      <UnkC8>基本設定</UnkC8>
    </Field>
    <Field Def="u32 lowerFpsContinousCount = 5">
      <Description>このフレーム連続してしきい値を下回ったら、レベルアップ</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>999</Maximum>
      <SortID>2</SortID>
      <UnkC8>基本設定</UnkC8>
    </Field>
    <Field Def="u32 upperFpsContinousCount = 20">
      <Description>このフレーム連続してしきい値を上回ったら、レベルダウン</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>999</Maximum>
      <SortID>3</SortID>
      <UnkC8>基本設定</UnkC8>
    </Field>
    <Field Def="u32 downAfterChangeSleep = 30">
      <Description>レベルダウン後のスリープフレームカウント</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>999</Maximum>
      <SortID>4</SortID>
      <UnkC8>基本設定</UnkC8>
    </Field>
    <Field Def="u32 upAfterChangeSleep = 10">
      <Description>レベルアップ後のスリープフレームカウント</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>999</Maximum>
      <SortID>5</SortID>
      <UnkC8>基本設定</UnkC8>
    </Field>
    <Field Def="u8 postProcessLightShaft = 20">
      <DisplayName>ライトシャフト</DisplayName>
      <Description>フィルタのライトシャフト</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>100</SortID>
      <UnkC8>ポストプロセス</UnkC8>
    </Field>
    <Field Def="u8 postProcessBloom = 20">
      <DisplayName>Bloom</DisplayName>
      <Description>ブルーム</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>101</SortID>
      <UnkC8>ポストプロセス</UnkC8>
    </Field>
    <Field Def="u8 postProcessGlow = 20">
      <DisplayName>Glow</DisplayName>
      <Description>グロー</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>102</SortID>
      <UnkC8>ポストプロセス</UnkC8>
    </Field>
    <Field Def="u8 postProcessAA = 20">
      <DisplayName>AA</DisplayName>
      <Description>アンチエイリアス</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>103</SortID>
      <UnkC8>ポストプロセス</UnkC8>
    </Field>
    <Field Def="u8 postProcessSSAO = 20">
      <DisplayName>SSAO</DisplayName>
      <Description>SSAO</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>104</SortID>
      <UnkC8>ポストプロセス</UnkC8>
    </Field>
    <Field Def="u8 postProcessDOF = 20">
      <DisplayName>DOF</DisplayName>
      <Description>DOF</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>105</SortID>
      <UnkC8>ポストプロセス</UnkC8>
    </Field>
    <Field Def="u8 postProcessMotionBlur = 20">
      <DisplayName>MotionBlur</DisplayName>
      <Description>MotionBlur</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>106</SortID>
      <UnkC8>ポストプロセス</UnkC8>
    </Field>
    <Field Def="u8 postProcessMotionBlurIteration = 20">
      <DisplayName>MotionBlurIteration</DisplayName>
      <Description>MotionBlurのイテレーション回数を下げる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>107</SortID>
      <UnkC8>ポストプロセス</UnkC8>
    </Field>
    <Field Def="dummy8 reserve0[1]">
      <DisplayName>予備</DisplayName>
      <Description>予備</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>108</SortID>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 shadowBlur = 20">
      <DisplayName>Shadow Blur</DisplayName>
      <Description>影のブラーを切る</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>109</SortID>
      <UnkC8>シャドウ</UnkC8>
    </Field>
    <Field Def="u8 sfxParticleHalf = 20">
      <DisplayName>SFXのエミット回り</DisplayName>
      <Description>エミット間隔、エミット数、LOD距離をグラフィックスコンフィグの半分に</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>110</SortID>
      <UnkC8>SFX</UnkC8>
    </Field>
    <Field Def="u8 sfxReflection = 20">
      <DisplayName>SFXの反射</DisplayName>
      <Description>反射シーンSFXをオミット</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>111</SortID>
      <UnkC8>SFX</UnkC8>
    </Field>
    <Field Def="u8 sfxWaterInteraction = 20">
      <DisplayName>水面インタラクション</DisplayName>
      <Description>水面インタラクトSFXをオミット</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>112</SortID>
      <UnkC8>SFX</UnkC8>
    </Field>
    <Field Def="u8 sfxGlow = 20">
      <DisplayName>SFXのグロー</DisplayName>
      <Description>SFXでかけてるGlowをオミット</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>113</SortID>
      <UnkC8>SFX</UnkC8>
    </Field>
    <Field Def="u8 sfxDistortion = 20">
      <DisplayName>SFXの歪み</DisplayName>
      <Description>SFXでかけてる歪みのオミット</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>114</SortID>
      <UnkC8>SFX</UnkC8>
    </Field>
    <Field Def="u8 sftSoftSprite = 20">
      <DisplayName>ソフトスプライト</DisplayName>
      <Description>SFXでかけてるソフトスプライトのオミット</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>115</SortID>
      <UnkC8>SFX</UnkC8>
    </Field>
    <Field Def="u8 sfxLightShaft = 20">
      <DisplayName>ライトシャフト</DisplayName>
      <Description>SFXのライトシャフトのオミット</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>116</SortID>
      <UnkC8>SFX</UnkC8>
    </Field>
    <Field Def="u8 sfxScaleRenderDistanceScale = 20">
      <DisplayName>動的に縮小バッファに登録されるエフェクトの距離判定にスケール</DisplayName>
      <Description>SFXの距離で動的に縮小バッファに登録されるエフェクトの距離判定にスケール</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>117</SortID>
      <UnkC8>SFX</UnkC8>
    </Field>
    <Field Def="u8 dynamicResolution = 1">
      <DisplayName>動的解像度</DisplayName>
      <Description>動的解像度</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>118</SortID>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 shadowCascade0ResolutionHalf">
      <DisplayName>Shadow Cascade0 ResolutionHalf</DisplayName>
      <Description>影（カスケード0）の解像度を半分に下げる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>119</SortID>
      <UnkC8>シャドウ</UnkC8>
    </Field>
    <Field Def="u8 shadowCascade1ResolutionHalf = 13">
      <DisplayName>Shadow Cascade1 ResolutionHalf</DisplayName>
      <Description>影（カスケード1）の解像度を半分に下げる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>120</SortID>
      <UnkC8>シャドウ</UnkC8>
    </Field>
    <Field Def="u8 chrWetDisablePlayer = 21">
      <DisplayName>ローカルプレイヤー</DisplayName>
      <Description>ローカルプレイヤーの水濡れ処理を切る</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>121</SortID>
      <UnkC8>キャラ水濡れOFF</UnkC8>
    </Field>
    <Field Def="u8 chrWetDisableRemotePlayer = 21">
      <DisplayName>リモートプレイヤー</DisplayName>
      <Description>リモートプレイヤーの水濡れ処理を切る</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>122</SortID>
      <UnkC8>キャラ水濡れOFF</UnkC8>
    </Field>
    <Field Def="u8 chrWetDisableEnemy = 21">
      <DisplayName>敵キャラ</DisplayName>
      <Description>敵キャラの水濡れ処理を切る</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>21</Maximum>
      <SortID>123</SortID>
      <UnkC8>キャラ水濡れOFF</UnkC8>
    </Field>
    <Field Def="u8 dynamicResolutionPercentageMin = 100">
      <DisplayName>解像度引き下げ 下限(%)</DisplayName>
      <Description>解像度引き下げ 下限(%)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>100</Maximum>
      <SortID>10</SortID>
      <UnkC8>動的解像度</UnkC8>
    </Field>
    <Field Def="u8 dynamicResolutionPercentageMax = 100">
      <DisplayName>解像度引き下げ 上限(%)</DisplayName>
      <Description>解像度引き下げ 上限(%)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>100</Maximum>
      <SortID>11</SortID>
      <UnkC8>動的解像度</UnkC8>
    </Field>
    <Field Def="dummy8 reserve1[30]">
      <DisplayName>予備</DisplayName>
      <Description>予備</Description>
      <SortID>124</SortID>
      <UnkC8>その他</UnkC8>
    </Field>
  </Fields>
</PARAMDEF>