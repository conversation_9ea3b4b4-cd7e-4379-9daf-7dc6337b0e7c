﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>SFX_BLOCK_RES_SHARE_PARAM</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u32 shareBlockRsMapUidVal">
      <DisplayName>ブロックSfxリソース参照先マップ番号</DisplayName>
      <Description>リソースを参照するマップ番号。マップ番号を数値化した値を設定します。(m12_34_56_78→12345678)</Description>
      <EditFlags>None</EditFlags>
      <Maximum>99999999</Maximum>
    </Field>
  </Fields>
</PARAMDEF>