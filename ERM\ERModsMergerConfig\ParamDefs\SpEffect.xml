﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>SP_EFFECT_PARAM_ST</ParamType>
  <DataVersion>4</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="s32 iconId = -1">
      <DisplayName>アイコンID</DisplayName>
      <Description>アイコンID(-1の時は、アイコン必要なし)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="f32 conditionHp = -1">
      <DisplayName>発動条件　残りHP比率[%]</DisplayName>
      <Description>残りHPが、maxHPの何%になったら発動するかを設定</Description>
      <Minimum>-1</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>18000</SortID>
    </Field>
    <Field Def="f32 effectEndurance">
      <DisplayName>効果持続時間　時間[s]</DisplayName>
      <Description>変化持続時間　/-1で永続 /0で瞬間1回限り</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>19000</SortID>
    </Field>
    <Field Def="f32 motionInterval">
      <DisplayName>発動間隔[s]</DisplayName>
      <Description>何秒間隔で発生するのかを設定</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>20000</SortID>
    </Field>
    <Field Def="f32 maxHpRate = 1">
      <DisplayName>最大HP倍率[%]</DisplayName>
      <Description>最大HPに補正をかける</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>21000</SortID>
    </Field>
    <Field Def="f32 maxMpRate = 1">
      <DisplayName>最大MP倍率[%]</DisplayName>
      <Description>最大MPに補正をかける</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>22000</SortID>
    </Field>
    <Field Def="f32 maxStaminaRate = 1">
      <DisplayName>最大スタミナ倍率[%]</DisplayName>
      <Description>最大SPに補正をかける</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>23000</SortID>
    </Field>
    <Field Def="f32 slashDamageCutRate = 1">
      <DisplayName>防御側：斬撃ダメージ倍率</DisplayName>
      <Description>斬撃ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>29100</SortID>
    </Field>
    <Field Def="f32 blowDamageCutRate = 1">
      <DisplayName>防御側：打撃ダメージ倍率</DisplayName>
      <Description>打撃ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>29200</SortID>
    </Field>
    <Field Def="f32 thrustDamageCutRate = 1">
      <DisplayName>防御側：刺突ダメージ倍率</DisplayName>
      <Description>刺突ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>29300</SortID>
    </Field>
    <Field Def="f32 neutralDamageCutRate = 1">
      <DisplayName>防御側：無属性ダメージ倍率</DisplayName>
      <Description>無属性ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>29400</SortID>
    </Field>
    <Field Def="f32 magicDamageCutRate = 1">
      <DisplayName>防御側：魔法ダメージ倍率</DisplayName>
      <Description>魔法ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>30000</SortID>
    </Field>
    <Field Def="f32 fireDamageCutRate = 1">
      <DisplayName>防御側：炎ダメージ倍率</DisplayName>
      <Description>炎ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>31000</SortID>
    </Field>
    <Field Def="f32 thunderDamageCutRate = 1">
      <DisplayName>防御側：電撃ダメージ倍率</DisplayName>
      <Description>電撃ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>31100</SortID>
    </Field>
    <Field Def="f32 physicsAttackRate = 1">
      <DisplayName>攻撃側：物理ダメージ倍率</DisplayName>
      <Description>物理ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>32000</SortID>
    </Field>
    <Field Def="f32 magicAttackRate = 1">
      <DisplayName>攻撃側：魔法ダメージ倍率</DisplayName>
      <Description>魔法ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>33000</SortID>
    </Field>
    <Field Def="f32 fireAttackRate = 1">
      <DisplayName>攻撃側：炎ダメージ倍率</DisplayName>
      <Description>炎ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>34000</SortID>
    </Field>
    <Field Def="f32 thunderAttackRate = 1">
      <DisplayName>攻撃側：電撃ダメージ倍率</DisplayName>
      <Description>電撃ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>34100</SortID>
    </Field>
    <Field Def="f32 physicsAttackPowerRate = 1">
      <DisplayName>物理攻撃力倍率</DisplayName>
      <Description>物理攻撃力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>35000</SortID>
    </Field>
    <Field Def="f32 magicAttackPowerRate = 1">
      <DisplayName>魔法攻撃力倍率</DisplayName>
      <Description>魔法攻撃力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>36000</SortID>
    </Field>
    <Field Def="f32 fireAttackPowerRate = 1">
      <DisplayName>炎攻撃力倍率</DisplayName>
      <Description>炎攻撃力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>37000</SortID>
    </Field>
    <Field Def="f32 thunderAttackPowerRate = 1">
      <DisplayName>電撃攻撃力倍率</DisplayName>
      <Description>電撃攻撃力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>37100</SortID>
    </Field>
    <Field Def="s32 physicsAttackPower">
      <DisplayName>物理攻撃力[point]</DisplayName>
      <Description>物理攻撃力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>38000</SortID>
    </Field>
    <Field Def="s32 magicAttackPower">
      <DisplayName>魔法攻撃力[point]</DisplayName>
      <Description>魔法攻撃力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>39000</SortID>
    </Field>
    <Field Def="s32 fireAttackPower">
      <DisplayName>炎攻撃力[point]</DisplayName>
      <Description>炎攻撃力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>40000</SortID>
    </Field>
    <Field Def="s32 thunderAttackPower">
      <DisplayName>電撃攻撃力[point]</DisplayName>
      <Description>電撃攻撃力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>40100</SortID>
    </Field>
    <Field Def="f32 physicsDiffenceRate = 1">
      <DisplayName>物理防御力倍率</DisplayName>
      <Description>物理防御力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>41000</SortID>
    </Field>
    <Field Def="f32 magicDiffenceRate = 1">
      <DisplayName>魔法防御力倍率</DisplayName>
      <Description>魔法防御力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>42000</SortID>
    </Field>
    <Field Def="f32 fireDiffenceRate = 1">
      <DisplayName>炎防御力倍率</DisplayName>
      <Description>炎防御力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>43000</SortID>
    </Field>
    <Field Def="f32 thunderDiffenceRate = 1">
      <DisplayName>電撃防御力倍率</DisplayName>
      <Description>電撃防御力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>43100</SortID>
    </Field>
    <Field Def="s32 physicsDiffence">
      <DisplayName>物理防御力[point]</DisplayName>
      <Description>物理防御力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>44000</SortID>
    </Field>
    <Field Def="s32 magicDiffence">
      <DisplayName>魔法防御力[point]</DisplayName>
      <Description>魔法防御力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>45000</SortID>
    </Field>
    <Field Def="s32 fireDiffence">
      <DisplayName>炎防御力[point]</DisplayName>
      <Description>炎防御力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>46000</SortID>
    </Field>
    <Field Def="s32 thunderDiffence">
      <DisplayName>電撃防御力[point]</DisplayName>
      <Description>電撃防御力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>46100</SortID>
    </Field>
    <Field Def="f32 NoGuardDamageRate = 1">
      <DisplayName>隙ダメージ倍率</DisplayName>
      <Description>隙のときのダメージ倍率を、設定した数値に置き換える（ダメージ側に設定）</Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <SortID>47000</SortID>
    </Field>
    <Field Def="f32 vitalSpotChangeRate = -1">
      <DisplayName>スィートスポット倍率</DisplayName>
      <Description>スィートスポットのダメージ計算を指定した数値に差し替える(急所ダメージ補正) -1.0で無効</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>48000</SortID>
    </Field>
    <Field Def="f32 normalSpotChangeRate = -1">
      <DisplayName>ノーマルヒット倍率</DisplayName>
      <Description>ノーマルヒットのダメージ計算を指定した数値に差し替える  -1.0で無効</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>49000</SortID>
    </Field>
    <Field Def="f32 lookAtTargetPosOffset">
      <DisplayName>LookAt位置オフセット[m]</DisplayName>
      <Description>敵がLookAtする際に目標位置をオフセットする。見られる側のしゃがみや騎乗に設定する</Description>
      <Minimum>-99.99</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>75600</SortID>
    </Field>
    <Field Def="s32 behaviorId = -1">
      <DisplayName>行動ID指定枠</DisplayName>
      <Description>特殊効果から行動IDを使ってダメージを与える場合に指定-1で無効</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>50100</SortID>
    </Field>
    <Field Def="f32 changeHpRate">
      <DisplayName>HPダメージ量[%]</DisplayName>
      <Description>一度の発動で最大HPの何%分を減算（または加算）するかを設定</Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>51000</SortID>
    </Field>
    <Field Def="s32 changeHpPoint">
      <DisplayName>HPダメージ[point]</DisplayName>
      <Description>一度の発動で何ポイント減算（または加算）するかを設定</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>52000</SortID>
    </Field>
    <Field Def="f32 changeMpRate">
      <DisplayName>MPダメージ量[%]</DisplayName>
      <Description>一度の発動で最大MPの何%分を減算（または加算）するかを設定</Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>53000</SortID>
    </Field>
    <Field Def="s32 changeMpPoint">
      <DisplayName>MPダメージ[point]</DisplayName>
      <Description>一度の発動で何ポイント減算（または加算）するかを設定</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>54000</SortID>
    </Field>
    <Field Def="s32 mpRecoverChangeSpeed">
      <DisplayName>MP回復速度変化[point]</DisplayName>
      <Description>回復速度を変化させる。回復計算式の基準回復速度、初期回復速度に加減算する。</Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <SortID>55000</SortID>
    </Field>
    <Field Def="f32 changeStaminaRate">
      <DisplayName>スタミナダメージ量[%]</DisplayName>
      <Description>一度の発動で最大スタミナの何%分を減算（または加算）するかを設定</Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <Increment>1</Increment>
      <SortID>56000</SortID>
    </Field>
    <Field Def="s32 changeStaminaPoint">
      <DisplayName>スタミナダメージ[point]</DisplayName>
      <Description>一度の発動で何ポイント減算（または加算）するかを設定</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>57000</SortID>
    </Field>
    <Field Def="s32 staminaRecoverChangeSpeed">
      <DisplayName>スタミナ回復速度変化[point]</DisplayName>
      <Description>回復速度を変化させる。回復計算式の基準回復速度、初期回復速度に加減算する。</Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <SortID>58000</SortID>
    </Field>
    <Field Def="f32 magicEffectTimeChange">
      <DisplayName>魔法効果時間変化</DisplayName>
      <Description>効果持続時間に0.1秒以上設定されている魔法のみ、効果持続時間に設定されている時間を加減算する</Description>
      <Minimum>-999</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>59000</SortID>
    </Field>
    <Field Def="s32 insideDurability">
      <DisplayName>耐久度変化：内部損耗度[point]</DisplayName>
      <Description>内部損耗度に数値分を加減算する</Description>
      <Minimum>-999</Minimum>
      <Maximum>999</Maximum>
      <SortID>60000</SortID>
    </Field>
    <Field Def="s32 maxDurability">
      <DisplayName>耐久度変化：最大損耗度変化[point]</DisplayName>
      <Description>耐久度の内部損耗度の最大値に、設定された数値を加算する</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <SortID>61000</SortID>
    </Field>
    <Field Def="f32 staminaAttackRate = 1">
      <DisplayName>スタミナ攻撃力倍率</DisplayName>
      <Description>スタミナ攻撃力に、倍率をかける(1.0 1倍 0.5 半分）</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>62000</SortID>
    </Field>
    <Field Def="s32 poizonAttackPower">
      <DisplayName>毒耐性攻撃力[point]</DisplayName>
      <Description>ヒットした時に、対象の【毒耐性値】に加算する数値</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>64000</SortID>
    </Field>
    <Field Def="s32 diseaseAttackPower">
      <DisplayName>疫病耐性攻撃力[point]</DisplayName>
      <Description>ヒットした時に、対象の【疫病耐性値】に加算する数値</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>65000</SortID>
    </Field>
    <Field Def="s32 bloodAttackPower">
      <DisplayName>出血耐性攻撃力[point]</DisplayName>
      <Description>ヒットした時に、対象の【出血耐性値】に加算する数値</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>66000</SortID>
    </Field>
    <Field Def="s32 curseAttackPower">
      <DisplayName>呪耐性攻撃力[point]</DisplayName>
      <Description>ヒットした時に、対象の【呪耐性値】に加算する数値</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>66100</SortID>
    </Field>
    <Field Def="f32 fallDamageRate">
      <DisplayName>落下ダメージ倍率</DisplayName>
      <Description>落下時のダメージ計算に倍率をかける</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>67000</SortID>
    </Field>
    <Field Def="f32 soulRate">
      <DisplayName>取得ソウル倍率</DisplayName>
      <Description>敵を倒した時の取得ソウル量が、指定倍数分上乗せされる</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>68000</SortID>
    </Field>
    <Field Def="f32 equipWeightChangeRate">
      <DisplayName>装備重量変化倍率</DisplayName>
      <Description>最大装備重量に、設定された倍率をかける</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>69000</SortID>
    </Field>
    <Field Def="f32 allItemWeightChangeRate">
      <DisplayName>所持重量変化倍率</DisplayName>
      <Description>最大所持重量に、設定された倍率をかける</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>70000</SortID>
    </Field>
    <Field Def="s32 soul">
      <DisplayName>ソウル加算</DisplayName>
      <Description>所持ソウルに、設定値を加算する</Description>
      <Minimum>-99999999</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>71000</SortID>
    </Field>
    <Field Def="s32 animIdOffset = -1">
      <DisplayName>アニメIDオフセット(無効-1)</DisplayName>
      <Description>アニメIDオフセット(無効-1)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>111500</SortID>
    </Field>
    <Field Def="f32 haveSoulRate = 1">
      <DisplayName>所持ソウル率</DisplayName>
      <Description>敵周回効果用。設定されているキャラから外にソウルが出て行く時に適用されます。</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>72000</SortID>
    </Field>
    <Field Def="f32 targetPriority">
      <DisplayName>ターゲット優先度加算分</DisplayName>
      <Description>マルチプレイ時、敵から優先的にターゲットとして狙われるようになる。プライオリティの加算。０がデフォルト。プラス値でよく狙われるようになる。マイナスは、－１まで。</Description>
      <Minimum>-1</Minimum>
      <Maximum>10</Maximum>
      <SortID>73000</SortID>
    </Field>
    <Field Def="f32 sightSearchEnemyRate = 1">
      <DisplayName>見られる方：視覚倍率</DisplayName>
      <Description>見つかりやすさを倍率で補正する</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>73620</SortID>
    </Field>
    <Field Def="f32 hearingSearchEnemyRate = 1">
      <DisplayName>聞かせる方：AI音半径倍率</DisplayName>
      <Description>発するAI音の大きさを倍率で補正する</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>73920</SortID>
    </Field>
    <Field Def="f32 grabityRate = 1">
      <DisplayName>グラビティ率</DisplayName>
      <Description>グラビティ率</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <Increment>0.001</Increment>
      <SortID>87000</SortID>
    </Field>
    <Field Def="f32 registPoizonChangeRate = 1">
      <DisplayName>毒耐性変化倍率</DisplayName>
      <Description>毒耐性値に設定された倍率をかける</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>88000</SortID>
    </Field>
    <Field Def="f32 registDiseaseChangeRate = 1">
      <DisplayName>疫病耐性変化倍率</DisplayName>
      <Description>疫病耐性値に設定された倍率をかける</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>88100</SortID>
    </Field>
    <Field Def="f32 registBloodChangeRate = 1">
      <DisplayName>出血耐性変化倍率</DisplayName>
      <Description>出血耐性値に設定された倍率をかける</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>88200</SortID>
    </Field>
    <Field Def="f32 registCurseChangeRate = 1">
      <DisplayName>呪耐性変化倍率</DisplayName>
      <Description>呪耐性値に設定された倍率をかける</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>88300</SortID>
    </Field>
    <Field Def="f32 soulStealRate">
      <DisplayName>ソウルスティール係数</DisplayName>
      <Description>NPCがソウルスティールで奪われるHPに対する防御力</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>91000</SortID>
    </Field>
    <Field Def="f32 lifeReductionRate">
      <DisplayName>防御：寿命係数</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>100000</SortID>
    </Field>
    <Field Def="f32 hpRecoverRate">
      <DisplayName>HP回復量係数</DisplayName>
      <Description>HPが減るときは、効かない。</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>101000</SortID>
    </Field>
    <Field Def="s32 replaceSpEffectId = -1">
      <DisplayName>差し替える特殊効果</DisplayName>
      <Description>寿命が尽きた時に追加される特殊効果ID(-1は無視)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>111000</SortID>
    </Field>
    <Field Def="s32 cycleOccurrenceSpEffectId = -1">
      <DisplayName>周期発生特殊効果</DisplayName>
      <Description>発動周期毎に発生する特殊効果ID(-1は無視)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>111010</SortID>
    </Field>
    <Field Def="s32 atkOccurrenceSpEffectId = -1">
      <DisplayName>攻撃発生特殊効果</DisplayName>
      <Description>攻撃Hit時に発生する特殊効果ID(-1は無視)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>111020</SortID>
    </Field>
    <Field Def="f32 guardDefFlickPowerRate = 1">
      <DisplayName>ガード時はじき防御力アップ倍率</DisplayName>
      <Description>ガード時のはじき防御力補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <SortID>27530</SortID>
    </Field>
    <Field Def="f32 guardStaminaCutRate = 1">
      <DisplayName>ガード時スタミナカット倍率</DisplayName>
      <Description>ガード時のスタミナカット率補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <SortID>62100</SortID>
    </Field>
    <Field Def="s16 rayCastPassedTime = -1">
      <DisplayName>視線通過：発動時間[ms]</DisplayName>
      <Description>視線通過：発動時間[ms]（邪眼用）</Description>
      <Minimum>-1</Minimum>
      <Maximum>30000</Maximum>
      <SortID>108000</SortID>
    </Field>
    <Field Def="u8 magicSubCategoryChange1">
      <DisplayName>対サブカテゴリパラメータ変化1</DisplayName>
      <Enum>ATK_SUB_CATEGORY</Enum>
      <Description>対サブカテゴリパラメータ変化1</Description>
      <SortID>26300</SortID>
    </Field>
    <Field Def="u8 magicSubCategoryChange2">
      <DisplayName>対サブカテゴリパラメータ変化2</DisplayName>
      <Enum>ATK_SUB_CATEGORY</Enum>
      <Description>対サブカテゴリパラメータ変化2</Description>
      <SortID>26310</SortID>
    </Field>
    <Field Def="s16 bowDistRate">
      <DisplayName>弓飛距離補正[％]</DisplayName>
      <Description>武器の飛距離補正に加算される補正値</Description>
      <Minimum>-100</Minimum>
      <Maximum>999</Maximum>
      <SortID>62200</SortID>
    </Field>
    <Field Def="u16 spCategory">
      <DisplayName>特殊効果カテゴリ</DisplayName>
      <Enum>SP_EFFECT_SPCATEGORY</Enum>
      <Description>特殊効果の上書きなどの挙動を決めるカテゴリ</Description>
      <Maximum>60000</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="u8 categoryPriority">
      <DisplayName>カテゴリ内優先度</DisplayName>
      <Description>同一カテゴリ内での優先度（低い方が優先）</Description>
      <SortID>2200</SortID>
    </Field>
    <Field Def="s8 saveCategory = -1">
      <DisplayName>保存カテゴリ</DisplayName>
      <Enum>SP_EFFECT_SAVE_CATEGORY</Enum>
      <Description>特殊効果を保存するカテゴリ</Description>
      <Minimum>-1</Minimum>
      <SortID>2300</SortID>
    </Field>
    <Field Def="u8 changeMagicSlot">
      <DisplayName>魔法登録枠変化　魔法スロット</DisplayName>
      <Description>魔法登録枠を指定数増やすことが出来る</Description>
      <Maximum>3</Maximum>
      <SortID>83000</SortID>
    </Field>
    <Field Def="u8 changeMiracleSlot">
      <DisplayName>奇跡登録枠変化　奇跡スロット</DisplayName>
      <Description>軌跡登録枠を指定数増やすことが出来る</Description>
      <Maximum>3</Maximum>
      <SortID>84000</SortID>
    </Field>
    <Field Def="s8 heroPointDamage">
      <DisplayName>人間性ダメージ値</DisplayName>
      <Description>人間性値に与えるダメージ値</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <SortID>112000</SortID>
    </Field>
    <Field Def="u8 defFlickPower">
      <DisplayName>はじき防御力_上書き</DisplayName>
      <Description>はじき防御力を上書きする値を設定</Description>
      <SortID>27510</SortID>
    </Field>
    <Field Def="u8 flickDamageCutRate">
      <DisplayName>はじき時ダメージ減衰率[%]_上書き</DisplayName>
      <Description>はじき時のダメージ減衰率を上書きする値を設定</Description>
      <Maximum>100</Maximum>
      <SortID>27520</SortID>
    </Field>
    <Field Def="u8 bloodDamageRate = 100">
      <DisplayName>出血ダメージ補正倍率</DisplayName>
      <Description>状態変化タイプ[出血]のPointダメージ、％ダメージの時のみ使用される補正値</Description>
      <SortID>90000</SortID>
    </Field>
    <Field Def="s8 dmgLv_None">
      <DisplayName>DL_ダメージなし（0）</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>ダメージLv0を差し替えるタイプを指定</Description>
      <Minimum>0</Minimum>
      <SortID>27110</SortID>
    </Field>
    <Field Def="s8 dmgLv_S">
      <DisplayName>DL_小（1）</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>ダメージLv1を差し替えるタイプを指定</Description>
      <Minimum>0</Minimum>
      <SortID>27130</SortID>
    </Field>
    <Field Def="s8 dmgLv_M">
      <DisplayName>DL_中（2）</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>ダメージLv2を差し替えるタイプを指定</Description>
      <Minimum>0</Minimum>
      <SortID>27140</SortID>
    </Field>
    <Field Def="s8 dmgLv_L">
      <DisplayName>DL_大（3）</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>ダメージLv3を差し替えるタイプを指定</Description>
      <Minimum>0</Minimum>
      <SortID>27150</SortID>
    </Field>
    <Field Def="s8 dmgLv_BlowM">
      <DisplayName>DL_吹っ飛び（4）</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>ダメージLv4を差し替えるタイプを指定</Description>
      <Minimum>0</Minimum>
      <SortID>27170</SortID>
    </Field>
    <Field Def="s8 dmgLv_Push">
      <DisplayName>DL_プッシュ（5）</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>ダメージLv5を差し替えるタイプを指定</Description>
      <Minimum>0</Minimum>
      <SortID>27200</SortID>
    </Field>
    <Field Def="s8 dmgLv_Strike">
      <DisplayName>DL_叩きつけ（6）</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>ダメージLv6を差し替えるタイプを指定</Description>
      <Minimum>0</Minimum>
      <SortID>27180</SortID>
    </Field>
    <Field Def="s8 dmgLv_BlowS">
      <DisplayName>DL_小吹っ飛び（7）</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>ダメージLv7を差し替えるタイプを指定</Description>
      <Minimum>0</Minimum>
      <SortID>27160</SortID>
    </Field>
    <Field Def="s8 dmgLv_Min">
      <DisplayName>DL_極小（8）</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>ダメージLv8を差し替えるタイプを指定</Description>
      <Minimum>0</Minimum>
      <SortID>27120</SortID>
    </Field>
    <Field Def="s8 dmgLv_Uppercut">
      <DisplayName>DL_打ち上げ（9）</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>ダメージLv9を差し替えるタイプを指定</Description>
      <Minimum>0</Minimum>
      <SortID>27190</SortID>
    </Field>
    <Field Def="s8 dmgLv_BlowLL">
      <DisplayName>DL_特大吹っ飛び(10)</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>ダメージLv10を差し替えるタイプを指定</Description>
      <Minimum>0</Minimum>
      <SortID>27200</SortID>
    </Field>
    <Field Def="s8 dmgLv_Breath">
      <DisplayName>DL_ブレス(11)</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>ダメージLv11を差し替えるタイプを指定</Description>
      <Minimum>0</Minimum>
      <SortID>27210</SortID>
    </Field>
    <Field Def="u8 atkAttribute = 254">
      <DisplayName>物理属性</DisplayName>
      <Enum>ATKPARAM_ATKATTR_TYPE</Enum>
      <Description>特殊効果に設定する物理属性</Description>
      <SortID>27400</SortID>
    </Field>
    <Field Def="u8 spAttribute = 254">
      <DisplayName>特殊属性</DisplayName>
      <Enum>ATKPARAM_SPATTR_TYPE</Enum>
      <Description>特殊効果に設定する特殊属性</Description>
      <SortID>27500</SortID>
    </Field>
    <Field Def="u16 stateInfo">
      <DisplayName>状態変化タイプ</DisplayName>
      <Enum>SP_EFFECT_TYPE</Enum>
      <Description>状態変化の判定フラグ</Description>
      <Maximum>60000</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="u8 wepParamChange">
      <DisplayName>対武器パラメータ変化</DisplayName>
      <Enum>SP_EFE_WEP_CHANGE_PARAM</Enum>
      <Description>どの武器に対して効果を発揮するかを指定する。制限無しの場合は敵も含めた全ての攻撃・防御が対象</Description>
      <SortID>24000</SortID>
    </Field>
    <Field Def="u8 moveType">
      <DisplayName>移動タイプ</DisplayName>
      <Enum>SP_EFFECT_MOVE_TYPE</Enum>
      <Description>移動タイプ。移動速度を変更する。</Description>
      <SortID>85000</SortID>
    </Field>
    <Field Def="u16 lifeReductionType">
      <DisplayName>防御：寿命減少タイプ</DisplayName>
      <Enum>SP_EFFECT_TYPE</Enum>
      <Maximum>255</Maximum>
      <SortID>99000</SortID>
    </Field>
    <Field Def="u8 throwCondition">
      <DisplayName>投げ条件</DisplayName>
      <Enum>SP_EFFECT_THROW_CONDITION_TYPE</Enum>
      <Description>投げ条件。投げマスクに影響する。</Description>
      <SortID>110000</SortID>
    </Field>
    <Field Def="s8 addBehaviorJudgeId_condition = -1">
      <DisplayName>行動判定IDに加算する条件値</DisplayName>
      <Description>行動判定ＩＤに値を加算する条件値(Def:-1)</Description>
      <Minimum>-1</Minimum>
      <Maximum>9</Maximum>
      <SortID>113000</SortID>
    </Field>
    <Field Def="u8 freezeDamageRate = 100">
      <DisplayName>冷気ダメージ補正倍率</DisplayName>
      <Description>状態変化タイプ[冷気]のPointダメージ、％ダメージの時のみ使用される補正値</Description>
      <SortID>90100</SortID>
    </Field>
    <Field Def="u8 effectTargetSelf:1">
      <DisplayName>効果対象：所属　自分</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この判定にチェックが入っている対象のみ効果を発揮する、デフォルトは×</Description>
      <Maximum>1</Maximum>
      <SortID>3000</SortID>
    </Field>
    <Field Def="u8 effectTargetFriend:1">
      <DisplayName>効果対象：所属　味方</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この判定にチェックが入っている対象のみ効果を発揮する、デフォルトは×</Description>
      <Maximum>1</Maximum>
      <SortID>4000</SortID>
    </Field>
    <Field Def="u8 effectTargetEnemy:1">
      <DisplayName>効果対象：所属　敵</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この判定にチェックが入っている対象のみ効果を発揮する、デフォルトは×</Description>
      <Maximum>1</Maximum>
      <SortID>5000</SortID>
    </Field>
    <Field Def="u8 effectTargetPlayer:1">
      <DisplayName>効果対象：操作　PC</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この判定にチェックが入っている対象のみ効果を発揮する、デフォルトは×</Description>
      <Maximum>1</Maximum>
      <SortID>6000</SortID>
    </Field>
    <Field Def="u8 effectTargetAI:1">
      <DisplayName>効果対象：操作　AI</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この判定にチェックが入っている対象のみ効果を発揮する、デフォルトは×</Description>
      <Maximum>1</Maximum>
      <SortID>7000</SortID>
    </Field>
    <Field Def="u8 effectTargetLive:1">
      <DisplayName>効果対象：状態　生存</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この判定にチェックが入っている対象のみ効果を発揮する、デフォルトは×</Description>
      <Maximum>1</Maximum>
      <SortID>8000</SortID>
    </Field>
    <Field Def="u8 effectTargetGhost:1">
      <DisplayName>効果対象：状態　全ゴースト</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この判定にチェックが入っている対象のみ効果を発揮する、デフォルトは×</Description>
      <Maximum>1</Maximum>
      <SortID>9000</SortID>
    </Field>
    <Field Def="u8 disableSleep:1">
      <DisplayName>睡眠無効</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この効果がかかっていると睡眠にかからなくなる</Description>
      <Maximum>1</Maximum>
      <SortID>104070</SortID>
    </Field>
    <Field Def="u8 disableMadness:1">
      <DisplayName>発狂無効</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この効果がかかっていると発狂にかからなくなる</Description>
      <Maximum>1</Maximum>
      <SortID>104080</SortID>
    </Field>
    <Field Def="u8 effectTargetAttacker:1">
      <DisplayName>効果対象：攻撃者に発動</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>ダメージ後に攻撃者に特殊効果を適用（防御側には入れない）</Description>
      <Maximum>1</Maximum>
      <SortID>12000</SortID>
    </Field>
    <Field Def="u8 dispIconNonactive:1">
      <DisplayName>発動してなくてもアイコン表示</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>発動待ちの状態でもアイコンを表示する。</Description>
      <Maximum>1</Maximum>
      <SortID>13000</SortID>
    </Field>
    <Field Def="u8 regainGaugeDamage:1">
      <DisplayName>リゲインゲージを発生させるか</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>リゲインゲージを発生させるか</Description>
      <Maximum>1</Maximum>
      <SortID>161000</SortID>
    </Field>
    <Field Def="u8 bAdjustMagicAblity:1">
      <DisplayName>魔力補正するか？</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>魔力補正するか？</Description>
      <Maximum>1</Maximum>
      <SortID>15000</SortID>
    </Field>
    <Field Def="u8 bAdjustFaithAblity:1">
      <DisplayName>信仰補正するか？</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>信仰補正するか？</Description>
      <Maximum>1</Maximum>
      <SortID>16000</SortID>
    </Field>
    <Field Def="u8 bGameClearBonus:1">
      <DisplayName>周回ボーナス用か？</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>ゲームクリア周回ボーナス用かどうか。</Description>
      <Maximum>1</Maximum>
      <SortID>17000</SortID>
    </Field>
    <Field Def="u8 magParamChange:1">
      <DisplayName>対魔法パラメータ変化</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>魔法に対して効果を発揮するかしないかを設定する</Description>
      <Maximum>1</Maximum>
      <SortID>25000</SortID>
    </Field>
    <Field Def="u8 miracleParamChange:1">
      <DisplayName>対奇跡パラメータ変化</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>奇跡に対して効果を発揮するかしないかを設定する</Description>
      <Maximum>1</Maximum>
      <SortID>26000</SortID>
    </Field>
    <Field Def="u8 clearSoul:1">
      <DisplayName>所持ソウルクリアするか</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>所持ソウルが0になります。</Description>
      <Maximum>1</Maximum>
      <SortID>27000</SortID>
    </Field>
    <Field Def="u8 requestSOS:1">
      <DisplayName>SOSサイン　判定フラグ</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>チェックが付いている場合、発動時にSOSサイン要求を発行</Description>
      <Maximum>1</Maximum>
      <SortID>76000</SortID>
    </Field>
    <Field Def="u8 requestBlackSOS:1">
      <DisplayName>ブラックSOSサイン　判定フラグ</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>チェックが付いている場合、発動時にブラックSOSサイン要求を発行</Description>
      <Maximum>1</Maximum>
      <SortID>77000</SortID>
    </Field>
    <Field Def="u8 requestForceJoinBlackSOS:1">
      <DisplayName>侵入_Aリクエスト　判定フラグ</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>チェックが付いている場合、発動時に侵入_Aリクエストを発行</Description>
      <Maximum>1</Maximum>
      <SortID>78000</SortID>
    </Field>
    <Field Def="u8 requestKickSession:1">
      <DisplayName>キック　判定フラグ</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>チェックが付いている場合、発動時にキック要求を発行</Description>
      <Maximum>1</Maximum>
      <SortID>79000</SortID>
    </Field>
    <Field Def="u8 requestLeaveSession:1">
      <DisplayName>退出　判定フラグ</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>チェックが付いている場合、発動時に退出要求を発行</Description>
      <Maximum>1</Maximum>
      <SortID>80000</SortID>
    </Field>
    <Field Def="u8 requestNpcInveda:1">
      <DisplayName>NPCへの侵入　判定フラグ</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>チェックが付いている場合、発動時にNPCへの侵入要求を発行</Description>
      <Maximum>1</Maximum>
      <SortID>80100</SortID>
    </Field>
    <Field Def="u8 noDead:1">
      <DisplayName>成仏不可　判定フラグ</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>死体状態になれるかどうか。このチェックが付いていると、死亡状態にならない</Description>
      <Maximum>1</Maximum>
      <SortID>81000</SortID>
    </Field>
    <Field Def="u8 bCurrHPIndependeMaxHP:1">
      <DisplayName>最大HPが増減しても、現在HPは影響しないか？</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>最大HPが増減しても、現在HPは影響しないか？</Description>
      <Maximum>1</Maximum>
      <SortID>82000</SortID>
    </Field>
    <Field Def="u8 corrosionIgnore:1">
      <DisplayName>腐食無視</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>【状態変化タイプ】が【腐食】による【耐久度】減少を無視する</Description>
      <Maximum>1</Maximum>
      <SortID>92000</SortID>
    </Field>
    <Field Def="u8 sightSearchCutIgnore:1">
      <DisplayName>視覚索敵カット無視</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>視覚索敵無効を無視する</Description>
      <Maximum>1</Maximum>
      <SortID>93000</SortID>
    </Field>
    <Field Def="u8 hearingSearchCutIgnore:1">
      <DisplayName>聴覚索敵カット無視</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>聴覚索敵無効を無視する</Description>
      <Maximum>1</Maximum>
      <SortID>94000</SortID>
    </Field>
    <Field Def="u8 antiMagicIgnore:1">
      <DisplayName>アンチマジック無効</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>アンチマジック範囲でも魔法を使用できる</Description>
      <Maximum>1</Maximum>
      <SortID>95000</SortID>
    </Field>
    <Field Def="u8 fakeTargetIgnore:1">
      <DisplayName>偽ターゲット無効_幻聴系</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>発生した偽ターゲットに引っかからなくなる</Description>
      <Maximum>1</Maximum>
      <SortID>96000</SortID>
    </Field>
    <Field Def="u8 fakeTargetIgnoreUndead:1">
      <DisplayName>偽ターゲット無効_人系</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>発生した人系の偽ターゲットに引っかからなくなる</Description>
      <Maximum>1</Maximum>
      <SortID>96500</SortID>
    </Field>
    <Field Def="u8 fakeTargetIgnoreAnimal:1">
      <DisplayName>偽ターゲット無効_獣系</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>発生した獣系の偽ターゲットに引っかからなくなる</Description>
      <Maximum>1</Maximum>
      <SortID>96600</SortID>
    </Field>
    <Field Def="u8 grabityIgnore:1">
      <DisplayName>グラビティ無効</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>グラビティ効果無効</Description>
      <Maximum>1</Maximum>
      <SortID>97000</SortID>
    </Field>
    <Field Def="u8 disablePoison:1">
      <DisplayName>毒無効</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この効果がかかっていると毒にかからなくなる</Description>
      <Maximum>1</Maximum>
      <SortID>102000</SortID>
    </Field>
    <Field Def="u8 disableDisease:1">
      <DisplayName>疫病無効</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この効果がかかっていると疫病にかからなくなる</Description>
      <Maximum>1</Maximum>
      <SortID>103000</SortID>
    </Field>
    <Field Def="u8 disableBlood:1">
      <DisplayName>出血無効</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この効果がかかっていると出血にかからなくなる</Description>
      <Maximum>1</Maximum>
      <SortID>104000</SortID>
    </Field>
    <Field Def="u8 disableCurse:1">
      <DisplayName>呪無効</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この効果がかかっていると呪いにかからなくなる</Description>
      <Maximum>1</Maximum>
      <SortID>104050</SortID>
    </Field>
    <Field Def="u8 enableCharm:1">
      <DisplayName>魅了有効</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この効果がかかっていると魅了にかかるようになる</Description>
      <Maximum>1</Maximum>
      <SortID>104100</SortID>
    </Field>
    <Field Def="u8 enableLifeTime:1">
      <DisplayName>寿命延長するか？</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>TAEによるフラグ設定時に寿命延長するか？</Description>
      <Maximum>1</Maximum>
      <SortID>105000</SortID>
    </Field>
    <Field Def="u8 bAdjustStrengthAblity:1">
      <DisplayName>筋力補正するか？</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>筋力補正するか？</Description>
      <Maximum>1</Maximum>
      <SortID>16100</SortID>
    </Field>
    <Field Def="u8 bAdjustAgilityAblity:1">
      <DisplayName>技量補正するか？</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>技量補正するか？</Description>
      <Maximum>1</Maximum>
      <SortID>16200</SortID>
    </Field>
    <Field Def="u8 eraseOnBonfireRecover:1">
      <DisplayName>篝火回復で消えるか</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>篝火回復で消えるか</Description>
      <Maximum>1</Maximum>
      <SortID>20600</SortID>
    </Field>
    <Field Def="u8 throwAttackParamChange:1">
      <DisplayName>対投げパラメータ変化</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>投げ攻撃に対して効果を発揮するかしないかを設定する</Description>
      <Maximum>1</Maximum>
      <SortID>26200</SortID>
    </Field>
    <Field Def="u8 requestLeaveColiseumSession:1">
      <DisplayName>闘技場退出　判定フラグ</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>チェックが付いている場合、発動時に闘技場退出要求を発行</Description>
      <Maximum>1</Maximum>
      <SortID>80000</SortID>
    </Field>
    <Field Def="u8 isExtendSpEffectLife:1">
      <DisplayName>寿命延長効果で延長するか？</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>寿命延長効果が掛かっている時に延長対象になるかどうか</Description>
      <Maximum>1</Maximum>
      <SortID>105100</SortID>
    </Field>
    <Field Def="u8 hasTarget:1">
      <DisplayName>敵を把握しているか？</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>敵を把握しているか？：[発動条件](邪眼使用者用)</Description>
      <Maximum>1</Maximum>
      <SortID>109000</SortID>
    </Field>
    <Field Def="u8 replanningOnFire:1">
      <DisplayName>発動時リプランニングするか？</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>発動時リプランニングするか</Description>
      <Maximum>1</Maximum>
      <SortID>72700</SortID>
    </Field>
    <Field Def="u8 vowType0:1">
      <DisplayName>誓約0</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約0</Description>
      <Maximum>1</Maximum>
      <SortID>130000</SortID>
    </Field>
    <Field Def="u8 vowType1:1">
      <DisplayName>誓約1</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約1</Description>
      <Maximum>1</Maximum>
      <SortID>130100</SortID>
    </Field>
    <Field Def="u8 vowType2:1">
      <DisplayName>誓約2</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約2</Description>
      <Maximum>1</Maximum>
      <SortID>130200</SortID>
    </Field>
    <Field Def="u8 vowType3:1">
      <DisplayName>誓約3</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約3</Description>
      <Maximum>1</Maximum>
      <SortID>130300</SortID>
    </Field>
    <Field Def="u8 vowType4:1">
      <DisplayName>誓約4</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約4</Description>
      <Maximum>1</Maximum>
      <SortID>130400</SortID>
    </Field>
    <Field Def="u8 vowType5:1">
      <DisplayName>誓約5</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約5</Description>
      <Maximum>1</Maximum>
      <SortID>130500</SortID>
    </Field>
    <Field Def="u8 vowType6:1">
      <DisplayName>誓約6</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約6</Description>
      <Maximum>1</Maximum>
      <SortID>130600</SortID>
    </Field>
    <Field Def="u8 vowType7:1">
      <DisplayName>誓約7</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約7</Description>
      <Maximum>1</Maximum>
      <SortID>130700</SortID>
    </Field>
    <Field Def="u8 vowType8:1">
      <DisplayName>誓約8</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約8</Description>
      <Maximum>1</Maximum>
      <SortID>130800</SortID>
    </Field>
    <Field Def="u8 vowType9:1">
      <DisplayName>誓約9</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約9</Description>
      <Maximum>1</Maximum>
      <SortID>130900</SortID>
    </Field>
    <Field Def="u8 vowType10:1">
      <DisplayName>誓約10</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約10</Description>
      <Maximum>1</Maximum>
      <SortID>131000</SortID>
    </Field>
    <Field Def="u8 vowType11:1">
      <DisplayName>誓約11</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約11</Description>
      <Maximum>1</Maximum>
      <SortID>131100</SortID>
    </Field>
    <Field Def="u8 vowType12:1">
      <DisplayName>誓約12</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約12</Description>
      <Maximum>1</Maximum>
      <SortID>131200</SortID>
    </Field>
    <Field Def="u8 vowType13:1">
      <DisplayName>誓約13</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約13</Description>
      <Maximum>1</Maximum>
      <SortID>131300</SortID>
    </Field>
    <Field Def="u8 vowType14:1">
      <DisplayName>誓約14</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約14</Description>
      <Maximum>1</Maximum>
      <SortID>131400</SortID>
    </Field>
    <Field Def="u8 vowType15:1">
      <DisplayName>誓約15</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>誓約15</Description>
      <Maximum>1</Maximum>
      <SortID>131500</SortID>
    </Field>
    <Field Def="s8 repAtkDmgLv">
      <DisplayName>攻撃側ダメージレベル差し替え</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>攻撃側のダメージレベルがこの値に指し換わる</Description>
      <Minimum>0</Minimum>
      <SortID>152000</SortID>
    </Field>
    <Field Def="f32 sightSearchRate = 1">
      <DisplayName>見る方：視覚倍率</DisplayName>
      <Description>見つけやすさを倍率で補正する</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>73720</SortID>
    </Field>
    <Field Def="u8 effectTargetOpposeTarget:1">
      <DisplayName>効果対象：●敵対</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この判定にチェックが入っている対象のみ効果を発揮する、デフォルトは×</Description>
      <Maximum>1</Maximum>
      <SortID>12100</SortID>
    </Field>
    <Field Def="u8 effectTargetFriendlyTarget:1">
      <DisplayName>効果対象：○味方</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この判定にチェックが入っている対象のみ効果を発揮する、デフォルトは×</Description>
      <Maximum>1</Maximum>
      <SortID>12200</SortID>
    </Field>
    <Field Def="u8 effectTargetSelfTarget:1">
      <DisplayName>効果対象：自分</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この判定にチェックが入っている対象のみ効果を発揮する、デフォルトは×</Description>
      <Maximum>1</Maximum>
      <SortID>12300</SortID>
    </Field>
    <Field Def="u8 effectTargetPcHorse:1">
      <DisplayName>効果対象：PC馬</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この判定にチェックが入っている対象のみ効果を発揮する、デフォルトは×</Description>
      <Maximum>1</Maximum>
      <SortID>12350</SortID>
    </Field>
    <Field Def="u8 effectTargetPcDeceased:1">
      <DisplayName>効果対象：PC亡者のみ</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この判定にチェックが入っている対象のみ効果を発揮する、デフォルトは×</Description>
      <Maximum>1</Maximum>
      <SortID>12400</SortID>
    </Field>
    <Field Def="u8 isContractSpEffectLife:1">
      <DisplayName>寿命短縮効果で短縮するか？</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>寿命短縮効果が掛かっている時に短縮対象になるかどうか</Description>
      <Maximum>1</Maximum>
      <SortID>105110</SortID>
    </Field>
    <Field Def="u8 isWaitModeDelete:1">
      <DisplayName>待ち状態に入ると削除</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>待ち状態になった瞬間に削除するか？</Description>
      <Maximum>1</Maximum>
      <SortID>411000</SortID>
    </Field>
    <Field Def="u8 isIgnoreNoDamage:1">
      <DisplayName>無敵時でも発動するか</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>状態変化タイプ「無敵時でも発動機能を適応」が掛かっているときのみ、無敵状態でもこの特殊効果からのダメージを適応するか</Description>
      <Maximum>1</Maximum>
      <SortID>411100</SortID>
    </Field>
    <Field Def="s8 changeTeamType = -1">
      <DisplayName>チームタイプ変更</DisplayName>
      <Enum>SP_EFFECT_CHANGE_TEAM_TYPE</Enum>
      <Description>指定したチームタイプに上書きする</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>84900</SortID>
    </Field>
    <Field Def="s16 dmypolyId = -1">
      <DisplayName>ダミポリID</DisplayName>
      <Description>ダミポリID。ダミポリID範囲は0～999.1000,10000の位はカテゴリ番号.</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>50200</SortID>
    </Field>
    <Field Def="s32 vfxId = -1">
      <DisplayName>特殊効果VfxId_０</DisplayName>
      <Description>特殊効果VfxId(-1無効)</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>14100</SortID>
    </Field>
    <Field Def="s32 accumuOverFireId = -1">
      <DisplayName>元気玉上限時発動特殊効果Id</DisplayName>
      <Description>元気玉上限時発動特殊効果Id</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>114200</SortID>
    </Field>
    <Field Def="s32 accumuOverVal = -1">
      <DisplayName>元気玉上限値</DisplayName>
      <Description>元気玉上限値</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>114100</SortID>
    </Field>
    <Field Def="s32 accumuUnderFireId = -1">
      <DisplayName>元気玉下限時発動特殊効果Id</DisplayName>
      <Description>元気玉下限時発動特殊効果Id</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>114400</SortID>
    </Field>
    <Field Def="s32 accumuUnderVal = -1">
      <DisplayName>元気玉下限値</DisplayName>
      <Description>元気玉下限値</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>114300</SortID>
    </Field>
    <Field Def="s32 accumuVal">
      <DisplayName>元気玉蓄積値</DisplayName>
      <Description>元気玉蓄積値</Description>
      <Minimum>-99999999</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>114500</SortID>
    </Field>
    <Field Def="u8 eye_angX">
      <DisplayName>見る方：視覚角度（高さ）上書き[deg]</DisplayName>
      <Description>見つけやすさの角度を上書きする</Description>
      <Maximum>180</Maximum>
      <SortID>73730</SortID>
    </Field>
    <Field Def="u8 eye_angY">
      <DisplayName>見る方：視覚角度（幅）上書き[deg]</DisplayName>
      <Description>見つけやすさの角度を上書きする</Description>
      <Maximum>180</Maximum>
      <SortID>73740</SortID>
    </Field>
    <Field Def="s16 addDeceasedLv">
      <DisplayName>亡者度 変更</DisplayName>
      <Description>この値分亡者度を加算する</Description>
      <Minimum>-999</Minimum>
      <Maximum>999</Maximum>
      <SortID>115000</SortID>
    </Field>
    <Field Def="s32 vfxId1 = -1">
      <DisplayName>特殊効果VfxId_１</DisplayName>
      <Description>特殊効果VfxId１(-1無効)</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>14110</SortID>
    </Field>
    <Field Def="s32 vfxId2 = -1">
      <DisplayName>特殊効果VfxId_２</DisplayName>
      <Description>特殊効果VfxId２(-1無効)</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>14120</SortID>
    </Field>
    <Field Def="s32 vfxId3 = -1">
      <DisplayName>特殊効果VfxId_３</DisplayName>
      <Description>特殊効果VfxId３(-1無効)</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>14130</SortID>
    </Field>
    <Field Def="s32 vfxId4 = -1">
      <DisplayName>特殊効果VfxId_４</DisplayName>
      <Description>特殊効果VfxId４(-1無効)</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>14140</SortID>
    </Field>
    <Field Def="s32 vfxId5 = -1">
      <DisplayName>特殊効果VfxId_５</DisplayName>
      <Description>特殊効果VfxId５(-1無効)</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>14150</SortID>
    </Field>
    <Field Def="s32 vfxId6 = -1">
      <DisplayName>特殊効果VfxId_６</DisplayName>
      <Description>特殊効果VfxId６(-1無効)</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>14160</SortID>
    </Field>
    <Field Def="s32 vfxId7 = -1">
      <DisplayName>特殊効果VfxId_７</DisplayName>
      <Description>特殊効果VfxId７(-1無効)</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>14170</SortID>
    </Field>
    <Field Def="s32 freezeAttackPower">
      <DisplayName>冷気耐性攻撃力[point]</DisplayName>
      <Description>ヒットした時に、対象の【冷気耐性値】に加算する数値</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>66200</SortID>
    </Field>
    <Field Def="s32 AppearAiSoundId">
      <DisplayName>発生AI音ID</DisplayName>
      <Description>設定された値のAI音パラメータを発生させる</Description>
      <Minimum>0</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>75200</SortID>
    </Field>
    <Field Def="s16 addFootEffectSfxId = -1">
      <DisplayName>追加フットエフェクト識別子</DisplayName>
      <Description>特殊効果時に追加で発生させるフットエフェクトの識別子。XYYZZZのZZZ</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>14500</SortID>
    </Field>
    <Field Def="s8 dexterityCancelSystemOnlyAddDexterity">
      <DisplayName>技量キャンセル用仮想ステータス</DisplayName>
      <Description>「技量キャンセル」のTAEフラグの終了タイミングを計算する時に、この値を追加して計算する</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <SortID>410000</SortID>
    </Field>
    <Field Def="s8 teamOffenseEffectivity = -1">
      <DisplayName>チーム攻撃影響力_上書き</DisplayName>
      <Description>対象の【チーム攻撃影響力】の値を、上書きして変更する。デフォルト値（-1）のときは変更しない。</Description>
      <Minimum>-1</Minimum>
      <Maximum>100</Maximum>
      <SortID>75300</SortID>
    </Field>
    <Field Def="f32 toughnessDamageCutRate = 1">
      <DisplayName>強靭度 被ダメージ倍率</DisplayName>
      <Description>強靭度版カット率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>90500</SortID>
    </Field>
    <Field Def="f32 weakDmgRateA = 1">
      <DisplayName>特攻Aダメージ倍率補正</DisplayName>
      <Description>特攻Aダメージ倍率に補正をかけます。１が通常。</Description>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>46500</SortID>
    </Field>
    <Field Def="f32 weakDmgRateB = 1">
      <DisplayName>特攻Bダメージ倍率補正</DisplayName>
      <Description>特攻Bダメージ倍率に補正をかけます。１が通常。</Description>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>46510</SortID>
    </Field>
    <Field Def="f32 weakDmgRateC = 1">
      <DisplayName>特攻Cダメージ倍率補正</DisplayName>
      <Description>特攻Cダメージ倍率に補正をかけます。１が通常。</Description>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>46520</SortID>
    </Field>
    <Field Def="f32 weakDmgRateD = 1">
      <DisplayName>特攻Dダメージ倍率補正</DisplayName>
      <Description>特攻Dダメージ倍率に補正をかけます。１が通常。</Description>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>46530</SortID>
    </Field>
    <Field Def="f32 weakDmgRateE = 1">
      <DisplayName>特攻Eダメージ倍率補正</DisplayName>
      <Description>特攻Eダメージ倍率に補正をかけます。１が通常。</Description>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>46540</SortID>
    </Field>
    <Field Def="f32 weakDmgRateF = 1">
      <DisplayName>特攻Fダメージ倍率補正</DisplayName>
      <Description>特攻Fダメージ倍率に補正をかけます。１が通常。</Description>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>46550</SortID>
    </Field>
    <Field Def="f32 darkDamageCutRate = 1">
      <DisplayName>防御側：闇ダメージ倍率</DisplayName>
      <Description>闇ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>31200</SortID>
    </Field>
    <Field Def="f32 darkDiffenceRate = 1">
      <DisplayName>闇防御力倍率</DisplayName>
      <Description>闇防御力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>43200</SortID>
    </Field>
    <Field Def="s32 darkDiffence">
      <DisplayName>闇防御力[point]</DisplayName>
      <Description>闇防御力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>46200</SortID>
    </Field>
    <Field Def="f32 darkAttackRate = 1">
      <DisplayName>攻撃側：闇ダメージ倍率</DisplayName>
      <Description>闇ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>34200</SortID>
    </Field>
    <Field Def="f32 darkAttackPowerRate = 1">
      <DisplayName>闇攻撃力倍率</DisplayName>
      <Description>闇攻撃力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>37200</SortID>
    </Field>
    <Field Def="s32 darkAttackPower">
      <DisplayName>闇攻撃力[point]</DisplayName>
      <Description>闇攻撃力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>40200</SortID>
    </Field>
    <Field Def="f32 antiDarkSightRadius">
      <DisplayName>暗闇丸見え半径[m]</DisplayName>
      <Description>暗闇丸見え半径[m]。この距離内にいる場合は暗所でも通常距離で見えるようになります</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>150000</SortID>
    </Field>
    <Field Def="s32 antiDarkSightDmypolyId = -1">
      <DisplayName>暗闇丸見えダミポリID</DisplayName>
      <Description>暗闇丸見えダミポリID(-1:マスター)。このダミポリを中心に丸見え領域を作成します</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999</Maximum>
      <SortID>151000</SortID>
    </Field>
    <Field Def="f32 conditionHpRate = -1">
      <DisplayName>発動条件　残りHP比率が一定以上[%]</DisplayName>
      <Description>指定された値以上のHPを持っている時にしか発動しない</Description>
      <Minimum>-1</Minimum>
      <Maximum>100</Maximum>
      <SortID>18500</SortID>
    </Field>
    <Field Def="f32 consumeStaminaRate = 1">
      <DisplayName>消費スタミナ倍率</DisplayName>
      <Description>行動パラメータの消費スタミナの値にかける倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>55900</SortID>
    </Field>
    <Field Def="f32 itemDropRate">
      <DisplayName>アイテムドロップ補正</DisplayName>
      <Description>設定された値が【アイテムドロップ補正】に加算される </Description>
      <Minimum>-99.99</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>72500</SortID>
    </Field>
    <Field Def="s32 changePoisonResistPoint">
      <DisplayName>毒耐性変化[point]</DisplayName>
      <Description>状態耐性値を増減させる</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>89000</SortID>
    </Field>
    <Field Def="s32 changeDiseaseResistPoint">
      <DisplayName>疫病耐性変化[point]</DisplayName>
      <Description>状態耐性値を増減させる</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>89100</SortID>
    </Field>
    <Field Def="s32 changeBloodResistPoint">
      <DisplayName>出血耐性変化[point]</DisplayName>
      <Description>状態耐性値を増減させる</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>89200</SortID>
    </Field>
    <Field Def="s32 changeCurseResistPoint">
      <DisplayName>呪耐性変化[point]</DisplayName>
      <Description>状態耐性値を増減させる</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>89300</SortID>
    </Field>
    <Field Def="s32 changeFreezeResistPoint">
      <DisplayName>冷気耐性変化[point]</DisplayName>
      <Description>状態耐性値を増減させる</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>89400</SortID>
    </Field>
    <Field Def="f32 slashAttackRate = 1">
      <DisplayName>攻撃側：斬撃ダメージ倍率</DisplayName>
      <Description>斬撃ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>32100</SortID>
    </Field>
    <Field Def="f32 blowAttackRate = 1">
      <DisplayName>攻撃側：打撃ダメージ倍率</DisplayName>
      <Description>打撃ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>32200</SortID>
    </Field>
    <Field Def="f32 thrustAttackRate = 1">
      <DisplayName>攻撃側：刺突ダメージ倍率</DisplayName>
      <Description>刺突ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>32300</SortID>
    </Field>
    <Field Def="f32 neutralAttackRate = 1">
      <DisplayName>攻撃側：無属性ダメージ倍率</DisplayName>
      <Description>無属性ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>32400</SortID>
    </Field>
    <Field Def="f32 slashAttackPowerRate = 1">
      <DisplayName>斬撃攻撃力倍率</DisplayName>
      <Description>斬撃攻撃力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>35100</SortID>
    </Field>
    <Field Def="f32 blowAttackPowerRate = 1">
      <DisplayName>打撃攻撃力倍率</DisplayName>
      <Description>打撃攻撃力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>35200</SortID>
    </Field>
    <Field Def="f32 thrustAttackPowerRate = 1">
      <DisplayName>刺突攻撃力倍率</DisplayName>
      <Description>刺突攻撃力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>35300</SortID>
    </Field>
    <Field Def="f32 neutralAttackPowerRate = 1">
      <DisplayName>無属性攻撃力倍率</DisplayName>
      <Description>無属性攻撃力に設定した数値をかけます</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>35400</SortID>
    </Field>
    <Field Def="s32 slashAttackPower">
      <DisplayName>斬撃攻撃力[point]</DisplayName>
      <Description>斬撃攻撃力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>38100</SortID>
    </Field>
    <Field Def="s32 blowAttackPower">
      <DisplayName>打撃攻撃力[point]</DisplayName>
      <Description>打撃攻撃力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>38200</SortID>
    </Field>
    <Field Def="s32 thrustAttackPower">
      <DisplayName>刺突攻撃力[point]</DisplayName>
      <Description>刺突攻撃力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>38300</SortID>
    </Field>
    <Field Def="s32 neutralAttackPower">
      <DisplayName>無属性攻撃力[point]</DisplayName>
      <Description>無属性攻撃力に設定した数値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>38400</SortID>
    </Field>
    <Field Def="s32 changeStrengthPoint">
      <DisplayName>筋力補正変化[point]</DisplayName>
      <Description>武器の補正値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>46600</SortID>
    </Field>
    <Field Def="s32 changeAgilityPoint">
      <DisplayName>俊敏補正変化[point]</DisplayName>
      <Description>武器の補正値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>46610</SortID>
    </Field>
    <Field Def="s32 changeMagicPoint">
      <DisplayName>魔力補正変化[point]</DisplayName>
      <Description>武器の補正値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>46700</SortID>
    </Field>
    <Field Def="s32 changeFaithPoint">
      <DisplayName>信仰補正変化[point]</DisplayName>
      <Description>武器の補正値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>46800</SortID>
    </Field>
    <Field Def="s32 changeLuckPoint">
      <DisplayName>運補正変化[point]</DisplayName>
      <Description>武器の補正値を加減算する</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>46900</SortID>
    </Field>
    <Field Def="s8 recoverArtsPoint_Str">
      <DisplayName>アーツポイント回復 筋力系</DisplayName>
      <Description>アーツポイント筋力を回復させる</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>153000</SortID>
    </Field>
    <Field Def="s8 recoverArtsPoint_Dex">
      <DisplayName>アーツポイント回復 技量系</DisplayName>
      <Description>アーツポイント技量を回復させる</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>153100</SortID>
    </Field>
    <Field Def="s8 recoverArtsPoint_Magic">
      <DisplayName>アーツポイント回復 魔法系</DisplayName>
      <Description>アーツポイント魔法を回復させる</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>153200</SortID>
    </Field>
    <Field Def="s8 recoverArtsPoint_Miracle">
      <DisplayName>アーツポイント回復 奇跡系</DisplayName>
      <Description>アーツポイント奇跡を回復させる</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>153300</SortID>
    </Field>
    <Field Def="u8 madnessDamageRate = 100">
      <DisplayName>発狂ダメージ補正倍率</DisplayName>
      <Description>状態変化タイプ[発狂]のPointダメージ、％ダメージの時のみ使用される補正値</Description>
      <SortID>90300</SortID>
    </Field>
    <Field Def="u8 isUseStatusAilmentAtkPowerCorrect:1">
      <DisplayName>状態異常攻撃力倍率補正を適応するか</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>○なら攻撃パラの状態異常攻撃力倍率補正を適応します。</Description>
      <Maximum>1</Maximum>
      <SortID>17500</SortID>
    </Field>
    <Field Def="u8 isUseAtkParamAtkPowerCorrect:1">
      <DisplayName>攻撃パラメータの攻撃力倍率補正を適応するか</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>○なら攻撃パラの攻撃力倍率補正を適応します。</Description>
      <Maximum>1</Maximum>
      <SortID>17550</SortID>
    </Field>
    <Field Def="u8 dontDeleteOnDead:1">
      <DisplayName>死亡時に削除しない</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>○ならキャラが死亡しても削除しません。主に死亡エフェクトに使います。</Description>
      <Maximum>1</Maximum>
      <SortID>20500</SortID>
    </Field>
    <Field Def="u8 disableFreeze:1">
      <DisplayName>冷気無効</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>この効果がかかっていると冷気にかからなくなる</Description>
      <Maximum>1</Maximum>
      <SortID>104060</SortID>
    </Field>
    <Field Def="u8 isDisableNetSync:1">
      <DisplayName>ネット同期しない</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>ネット同期しない。ローカルに掛けるようになる、という意味ではなく、単にネット送信しない。例えばリモートキャラはローカル発動しないので、その場合何も起こらない。</Description>
      <Maximum>1</Maximum>
      <SortID>420000</SortID>
    </Field>
    <Field Def="u8 shamanParamChange:1">
      <DisplayName>対呪術パラメータ変化</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>呪術に対して効果を発揮するかしないかを設定する</Description>
      <Maximum>1</Maximum>
      <SortID>26100</SortID>
    </Field>
    <Field Def="u8 isStopSearchedNotify:1">
      <DisplayName>被索敵状態の通知停止</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>自軍をターゲットしている通知を停止するかどうか(EventMakerでの判定やバディ小隊で使用)</Description>
      <Maximum>1</Maximum>
      <SortID>75500</SortID>
    </Field>
    <Field Def="u8 isCheckAboveShadowTest:1">
      <DisplayName>雨遮蔽外の時のみかかる</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>○なら遮蔽判定されているときは掛からない（×は常に掛かる）</Description>
      <Maximum>1</Maximum>
      <SortID>450000</SortID>
    </Field>
    <Field Def="u16 addBehaviorJudgeId_add">
      <DisplayName>行動判定IDに加算する加算値</DisplayName>
      <Description>行動判定IDの加算値 ０の場合は行動を切り替えるのではなく、行動しなくなります。</Description>
      <Maximum>999</Maximum>
      <SortID>114001</SortID>
    </Field>
    <Field Def="f32 saReceiveDamageRate = 1">
      <DisplayName>SA値_被ダメージ倍率</DisplayName>
      <Description>SAダメージかかる倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>90800</SortID>
    </Field>
    <Field Def="f32 defPlayerDmgCorrectRate_Physics = 1">
      <DisplayName>防御側 プレイヤー 物理ダメージ補正倍率</DisplayName>
      <Description>プレイヤーから受けるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46300</SortID>
    </Field>
    <Field Def="f32 defPlayerDmgCorrectRate_Magic = 1">
      <DisplayName>防御側 プレイヤー 魔法ダメージ補正倍率</DisplayName>
      <Description>プレイヤーから受けるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46310</SortID>
    </Field>
    <Field Def="f32 defPlayerDmgCorrectRate_Fire = 1">
      <DisplayName>防御側 プレイヤー 炎ダメージ補正倍率</DisplayName>
      <Description>プレイヤーから受けるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46320</SortID>
    </Field>
    <Field Def="f32 defPlayerDmgCorrectRate_Thunder = 1">
      <DisplayName>防御側 プレイヤー 雷ダメージ補正倍率</DisplayName>
      <Description>プレイヤーから受けるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46330</SortID>
    </Field>
    <Field Def="f32 defPlayerDmgCorrectRate_Dark = 1">
      <DisplayName>防御側 プレイヤー 闇ダメージ補正倍率</DisplayName>
      <Description>プレイヤーから受けるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46340</SortID>
    </Field>
    <Field Def="f32 defEnemyDmgCorrectRate_Physics = 1">
      <DisplayName>防御側 敵 物理ダメージ補正倍率</DisplayName>
      <Description>敵から受けるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46350</SortID>
    </Field>
    <Field Def="f32 defEnemyDmgCorrectRate_Magic = 1">
      <DisplayName>防御側 敵 魔法ダメージ補正倍率</DisplayName>
      <Description>敵から受けるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46360</SortID>
    </Field>
    <Field Def="f32 defEnemyDmgCorrectRate_Fire = 1">
      <DisplayName>防御側 敵 炎ダメージ補正倍率</DisplayName>
      <Description>敵から受けるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46370</SortID>
    </Field>
    <Field Def="f32 defEnemyDmgCorrectRate_Thunder = 1">
      <DisplayName>防御側 敵 雷ダメージ補正倍率</DisplayName>
      <Description>敵から受けるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46380</SortID>
    </Field>
    <Field Def="f32 defEnemyDmgCorrectRate_Dark = 1">
      <DisplayName>防御側 敵 闇ダメージ補正倍率</DisplayName>
      <Description>敵から受けるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46390</SortID>
    </Field>
    <Field Def="f32 defObjDmgCorrectRate = 1">
      <DisplayName>防御側 オブジェクトダメージ補正倍率</DisplayName>
      <Description>OBJから受けるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46400</SortID>
    </Field>
    <Field Def="f32 atkPlayerDmgCorrectRate_Physics = 1">
      <DisplayName>攻撃側 プレイヤー 物理ダメージ補正倍率</DisplayName>
      <Description>プレイヤーに与えるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46290</SortID>
    </Field>
    <Field Def="f32 atkPlayerDmgCorrectRate_Magic = 1">
      <DisplayName>攻撃側 プレイヤー 魔法ダメージ補正倍率</DisplayName>
      <Description>プレイヤーに与えるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46291</SortID>
    </Field>
    <Field Def="f32 atkPlayerDmgCorrectRate_Fire = 1">
      <DisplayName>攻撃側 プレイヤー 炎ダメージ補正倍率</DisplayName>
      <Description>プレイヤーに与えるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46292</SortID>
    </Field>
    <Field Def="f32 atkPlayerDmgCorrectRate_Thunder = 1">
      <DisplayName>攻撃側 プレイヤー 雷ダメージ補正倍率</DisplayName>
      <Description>プレイヤーに与えるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46293</SortID>
    </Field>
    <Field Def="f32 atkPlayerDmgCorrectRate_Dark = 1">
      <DisplayName>攻撃側 プレイヤー 闇ダメージ補正倍率</DisplayName>
      <Description>プレイヤーに与えるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46294</SortID>
    </Field>
    <Field Def="f32 atkEnemyDmgCorrectRate_Physics = 1">
      <DisplayName>攻撃側 敵 物理ダメージ補正倍率</DisplayName>
      <Description>敵に与えるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46295</SortID>
    </Field>
    <Field Def="f32 atkEnemyDmgCorrectRate_Magic = 1">
      <DisplayName>攻撃側 敵 魔法ダメージ補正倍率</DisplayName>
      <Description>敵に与えるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46296</SortID>
    </Field>
    <Field Def="f32 atkEnemyDmgCorrectRate_Fire = 1">
      <DisplayName>攻撃側 敵 炎ダメージ補正倍率</DisplayName>
      <Description>敵に与えるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46297</SortID>
    </Field>
    <Field Def="f32 atkEnemyDmgCorrectRate_Thunder = 1">
      <DisplayName>攻撃側 敵 雷ダメージ補正倍率</DisplayName>
      <Description>敵に与えるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46298</SortID>
    </Field>
    <Field Def="f32 atkEnemyDmgCorrectRate_Dark = 1">
      <DisplayName>攻撃側 敵 闇ダメージ補正倍率</DisplayName>
      <Description>敵に与えるダメージ値に対するダメージ補正。</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>46299</SortID>
    </Field>
    <Field Def="f32 registFreezeChangeRate = 1">
      <DisplayName>冷気耐性変化倍率</DisplayName>
      <Description>冷気耐性値に設定された倍率をかける</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>88400</SortID>
    </Field>
    <Field Def="u16 invocationConditionsStateChange1">
      <DisplayName>発動条件状態変化タイプ1</DisplayName>
      <Enum>SP_EFFECT_TYPE</Enum>
      <Description>発動条件状態変化タイプ1</Description>
      <Maximum>60000</Maximum>
      <SortID>165100</SortID>
    </Field>
    <Field Def="u16 invocationConditionsStateChange2">
      <DisplayName>発動条件状態変化タイプ2</DisplayName>
      <Enum>SP_EFFECT_TYPE</Enum>
      <Description>発動条件状態変化タイプ2</Description>
      <Maximum>60000</Maximum>
      <SortID>165200</SortID>
    </Field>
    <Field Def="u16 invocationConditionsStateChange3">
      <DisplayName>発動条件状態変化タイプ3</DisplayName>
      <Enum>SP_EFFECT_TYPE</Enum>
      <Description>発動条件状態変化タイプ3</Description>
      <Maximum>60000</Maximum>
      <SortID>165300</SortID>
    </Field>
    <Field Def="s16 hearingAiSoundLevel = -1">
      <DisplayName>聞く方：可聴AI音レベル上書き</DisplayName>
      <Description>どれくらい耳が良いのかを上書きする</Description>
      <Minimum>-1</Minimum>
      <Maximum>128</Maximum>
      <SortID>73850</SortID>
    </Field>
    <Field Def="f32 chrProxyHeightRate = 1">
      <DisplayName>カプセルサイズ倍率</DisplayName>
      <Description>キャラカプセルの高さに掛かる倍率</Description>
      <DisplayFormat>%0.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>75700</SortID>
    </Field>
    <Field Def="f32 addAwarePointCorrectValue_forMe">
      <DisplayName>索敵度加算補正_見る側</DisplayName>
      <Description>索敵度加算補正_見る側</Description>
      <DisplayFormat>%0.2f</DisplayFormat>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>73500</SortID>
    </Field>
    <Field Def="f32 addAwarePointCorrectValue_forTarget">
      <DisplayName>索敵度加算補正_見られる側</DisplayName>
      <Description>索敵度加算補正_見られる側</Description>
      <DisplayFormat>%0.2f</DisplayFormat>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>73510</SortID>
    </Field>
    <Field Def="f32 sightSearchEnemyAdd">
      <DisplayName>見られる方：視覚加算</DisplayName>
      <Description>見つかりやすさを実数で補正する</Description>
      <DisplayFormat>%0.2f</DisplayFormat>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>73610</SortID>
    </Field>
    <Field Def="f32 sightSearchAdd">
      <DisplayName>見る方：視覚加算</DisplayName>
      <Description>見つけやすさを実数で補正する</Description>
      <DisplayFormat>%0.2f</DisplayFormat>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>73710</SortID>
    </Field>
    <Field Def="f32 hearingSearchAdd">
      <DisplayName>聞く方：AI音半径加算</DisplayName>
      <Description>AI音の聞こえ具合を実数で補正する</Description>
      <DisplayFormat>%0.2f</DisplayFormat>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>73810</SortID>
    </Field>
    <Field Def="f32 hearingSearchRate = 1">
      <DisplayName>聞く方：AI音半径倍率</DisplayName>
      <Description>AI音の聞こえ具合を倍率で補正する</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>73820</SortID>
    </Field>
    <Field Def="f32 hearingSearchEnemyAdd">
      <DisplayName>聞かせる方：AI音半径加算</DisplayName>
      <Description>発するAI音の大きさを実数で補正する</Description>
      <DisplayFormat>%0.2f</DisplayFormat>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>73910</SortID>
    </Field>
    <Field Def="f32 value_Magnification = 1">
      <DisplayName>販売価格補正：倍率</DisplayName>
      <Description>販売価格補正：倍率</Description>
      <Minimum>1</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>200000</SortID>
    </Field>
    <Field Def="f32 artsConsumptionRate = 1">
      <DisplayName>アーツ消費MP倍率</DisplayName>
      <Description>アーツ消費MP倍率[%]</Description>
      <Minimum>0</Minimum>
      <Maximum>990</Maximum>
      <Increment>0.001</Increment>
      <SortID>300000</SortID>
    </Field>
    <Field Def="f32 magicConsumptionRate = 1">
      <DisplayName>魔法消費MP倍率</DisplayName>
      <Description>魔法消費MP倍率[%]</Description>
      <Minimum>0</Minimum>
      <Maximum>990</Maximum>
      <Increment>0.001</Increment>
      <SortID>300010</SortID>
    </Field>
    <Field Def="f32 shamanConsumptionRate = 1">
      <DisplayName>呪術消費MP倍率</DisplayName>
      <Description>呪術消費MP倍率[%]</Description>
      <Minimum>0</Minimum>
      <Maximum>990</Maximum>
      <Increment>0.001</Increment>
      <SortID>300020</SortID>
    </Field>
    <Field Def="f32 miracleConsumptionRate = 1">
      <DisplayName>奇跡消費MP倍率</DisplayName>
      <Description>奇跡消費MP倍率[%]</Description>
      <Minimum>0</Minimum>
      <Maximum>990</Maximum>
      <Increment>0.001</Increment>
      <SortID>300030</SortID>
    </Field>
    <Field Def="s32 changeHpEstusFlaskRate">
      <DisplayName>エスト瓶HPダメージ量[%]</DisplayName>
      <Description>一度の発動で最大HPの何%分を加算（または減算）するかを設定</Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <SortID>55200</SortID>
    </Field>
    <Field Def="s32 changeHpEstusFlaskPoint">
      <DisplayName>エスト瓶HPダメージ量[point]</DisplayName>
      <Description>一度の発動で何ポイント加算（または減算）するかを設定</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>55210</SortID>
    </Field>
    <Field Def="s32 changeMpEstusFlaskRate">
      <DisplayName>エスト瓶MPダメージ量[%] </DisplayName>
      <Description>一度の発動で最大MPの何%分を加算（または減算）するかを設定</Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <SortID>55220</SortID>
    </Field>
    <Field Def="s32 changeMpEstusFlaskPoint">
      <DisplayName>エスト瓶MPダメージ量[point] </DisplayName>
      <Description>一度の発動で何ポイント加算（または減算）するかを設定</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <SortID>55230</SortID>
    </Field>
    <Field Def="f32 changeHpEstusFlaskCorrectRate = 1">
      <DisplayName>エスト瓶HPダメージ倍率 </DisplayName>
      <Description>HPエスト瓶のダメージ量に対して補正をかける</Description>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>0.001</Increment>
      <SortID>55240</SortID>
    </Field>
    <Field Def="f32 changeMpEstusFlaskCorrectRate = 1">
      <DisplayName>エスト瓶MPダメージ倍率 </DisplayName>
      <Description>MPエスト瓶のダメージ量に対して補正をかける</Description>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>0.001</Increment>
      <SortID>55250</SortID>
    </Field>
    <Field Def="s32 applyIdOnGetSoul">
      <DisplayName>HPドレイン発動特殊効果</DisplayName>
      <Description>状態変化タイプ「HPドレイン」の特殊効果が有効の時に、敵を倒した際に同じ特殊効果の「HPドレイン発動特殊効果」に設定されている特殊効果IDを呼び出す(0：無視)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>111030</SortID>
    </Field>
    <Field Def="f32 extendLifeRate = 1">
      <DisplayName>寿命延長倍率</DisplayName>
      <Description>状態変化タイプ「寿命延長」の延長係数</Description>
      <Minimum>0</Minimum>
      <Maximum>9900</Maximum>
      <SortID>105101</SortID>
    </Field>
    <Field Def="f32 contractLifeRate = 1">
      <DisplayName>寿命短縮倍率</DisplayName>
      <Description>状態変化タイプ「寿命短縮」の短縮係数</Description>
      <Minimum>0</Minimum>
      <Maximum>9900</Maximum>
      <SortID>105111</SortID>
    </Field>
    <Field Def="f32 defObjectAttackPowerRate = 1">
      <DisplayName>被ダメージ オブジェクト攻撃力倍率</DisplayName>
      <Description>OBJから受けるダメージに対して攻撃力を補正する。（ダメージ補正ではない）</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>46450</SortID>
    </Field>
    <Field Def="s16 effectEndDeleteDecalGroupId = -1">
      <DisplayName>特殊効果消失時にキャラのペイントデカールを削除するグループID</DisplayName>
      <Description>特殊効果が消失した時（寿命/何かに上書きされる/消される…など）に、同じグループIDの特殊効果がかかっていなければペイントデカールを削除する。</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>410000</SortID>
    </Field>
    <Field Def="s8 addLifeForceStatus">
      <DisplayName>生命力追加値</DisplayName>
      <Description>成長ステータスに値を加える</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>401000</SortID>
    </Field>
    <Field Def="s8 addWillpowerStatus">
      <DisplayName>精神力追加値</DisplayName>
      <Description>成長ステータスに値を加える</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>401100</SortID>
    </Field>
    <Field Def="s8 addEndureStatus">
      <DisplayName>持久力追加値</DisplayName>
      <Description>成長ステータスに値を加える</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>401200</SortID>
    </Field>
    <Field Def="s8 addVitalityStatus">
      <DisplayName>体力追加値</DisplayName>
      <Description>成長ステータスに値を加える</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>401300</SortID>
    </Field>
    <Field Def="s8 addStrengthStatus">
      <DisplayName>筋力追加値</DisplayName>
      <Description>成長ステータスに値を加える</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>401400</SortID>
    </Field>
    <Field Def="s8 addDexterityStatus">
      <DisplayName>技量追加値</DisplayName>
      <Description>成長ステータスに値を加える</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>401500</SortID>
    </Field>
    <Field Def="s8 addMagicStatus">
      <DisplayName>理力追加値</DisplayName>
      <Description>成長ステータスに値を加える</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>401600</SortID>
    </Field>
    <Field Def="s8 addFaithStatus">
      <DisplayName>信仰追加値</DisplayName>
      <Description>成長ステータスに値を加える</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>401700</SortID>
    </Field>
    <Field Def="s8 addLuckStatus">
      <DisplayName>運追加値</DisplayName>
      <Description>成長ステータスに値を加える</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>401800</SortID>
    </Field>
    <Field Def="u8 deleteCriteriaDamage">
      <DisplayName>削除条件ダメージ</DisplayName>
      <Enum>SP_EFFECT_PARAM_DELETE_DAMAGE_TYPE</Enum>
      <Description>特殊効果を削除する条件のダメージ理由</Description>
      <SortID>18600</SortID>
    </Field>
    <Field Def="u8 magicSubCategoryChange3">
      <DisplayName>対サブカテゴリパラメータ変化3</DisplayName>
      <Enum>ATK_SUB_CATEGORY</Enum>
      <Description>対サブカテゴリパラメータ変化3</Description>
      <SortID>26320</SortID>
    </Field>
    <Field Def="u8 spAttributeVariationValue">
      <DisplayName>特殊属性バリエーション値</DisplayName>
      <Description>特殊効果に設定する特殊属性と組み合わせて状態異常SFX,SEなどにバリエーションを持たせるために使用する値です。SEQ16473</Description>
      <Maximum>99</Maximum>
      <SortID>27501</SortID>
    </Field>
    <Field Def="u8 atkFlickPower">
      <DisplayName>はじき攻撃力_上書き</DisplayName>
      <Description>はじき攻撃力を上書きする値を設定</Description>
      <SortID>27505</SortID>
    </Field>
    <Field Def="u8 wetConditionDepth">
      <DisplayName>濡れる条件の水位設定</DisplayName>
      <Enum>SP_EFFECT_WET_CONDITION_DEPTH</Enum>
      <Description>TimeAct「どの水位で濡れるか」と組み合わせて特殊効果に掛かるかどうかを判定する</Description>
      <SortID>460000</SortID>
    </Field>
    <Field Def="f32 changeSaRecoveryVelocity = 1">
      <DisplayName>SA回復速度変化</DisplayName>
      <Description>SA耐久度の回復速度を変化させる</Description>
      <Minimum>-99.99</Minimum>
      <Maximum>99.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>58300</SortID>
    </Field>
    <Field Def="f32 regainRate = 1">
      <DisplayName>リゲイン倍率</DisplayName>
      <Description>リゲイン倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>162000</SortID>
    </Field>
    <Field Def="f32 saAttackPowerRate = 1">
      <DisplayName>SA攻撃力倍率</DisplayName>
      <Description>SA攻撃力倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.1</Increment>
      <SortID>58400</SortID>
    </Field>
    <Field Def="s32 sleepAttackPower">
      <DisplayName>睡眠耐性攻撃力[point]</DisplayName>
      <Description>ヒットした時に、対象の【睡眠耐性値】に加算する数値</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>66300</SortID>
    </Field>
    <Field Def="s32 madnessAttackPower">
      <DisplayName>発狂耐性攻撃力[point]</DisplayName>
      <Description>ヒットした時に、対象の【発狂耐性値】に加算する数値</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>66400</SortID>
    </Field>
    <Field Def="f32 registSleepChangeRate = 1">
      <DisplayName>睡眠耐性変化倍率</DisplayName>
      <Description>睡眠耐性値に設定された倍率をかける</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>88500</SortID>
    </Field>
    <Field Def="f32 registMadnessChangeRate = 1">
      <DisplayName>発狂耐性変化倍率</DisplayName>
      <Description>発狂耐性値に設定された倍率をかける</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>88600</SortID>
    </Field>
    <Field Def="s32 changeSleepResistPoint">
      <DisplayName>睡眠耐性変化[point]</DisplayName>
      <Description>状態耐性値を増減させる</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>89500</SortID>
    </Field>
    <Field Def="s32 changeMadnessResistPoint">
      <DisplayName>発狂耐性変化[point]</DisplayName>
      <Description>状態耐性値を増減させる</Description>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <SortID>89600</SortID>
    </Field>
    <Field Def="u8 sleepDamageRate = 100">
      <DisplayName>睡眠ダメージ補正倍率</DisplayName>
      <Description>状態変化タイプ[睡眠]のPointダメージ、％ダメージの時のみ使用される補正値</Description>
      <SortID>90200</SortID>
    </Field>
    <Field Def="u8 applyPartsGroup">
      <DisplayName>対部位パラメータ変化</DisplayName>
      <Enum>SP_EFFECT_APPLY_PARTS_GROUP</Enum>
      <Description>攻撃がヒットした部位によって効果を制限する。ダメージ計算の防御系の項目のみ制限対象となる</Description>
      <Maximum>31</Maximum>
      <SortID>26400</SortID>
    </Field>
    <Field Def="u8 clearTarget:1">
      <DisplayName>ターゲットクリア</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>特殊効果が掛かっている間ターゲットを認識しない（騎乗ターゲット除く</Description>
      <Maximum>1</Maximum>
      <SortID>72710</SortID>
    </Field>
    <Field Def="u8 fakeTargetIgnoreAjin:1">
      <DisplayName>偽ターゲット無効_亜人系</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>発生した亜人系の偽ターゲットに引っかからなくなる</Description>
      <Maximum>1</Maximum>
      <SortID>96602</SortID>
    </Field>
    <Field Def="u8 fakeTargetIgnoreMirageArts:1">
      <DisplayName>偽ターゲット無効_幻影アーツ系</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>発生した幻影アーツ系の偽ターゲットに引っかからなくなる</Description>
      <Maximum>1</Maximum>
      <SortID>96604</SortID>
    </Field>
    <Field Def="u8 requestForceJoinBlackSOS_B:1">
      <DisplayName>侵入_Bリクエスト　判定フラグ</DisplayName>
      <Enum>SP_EFFECT_BOOL</Enum>
      <Description>チェックが付いている場合、発動時に侵入_Bリクエストを発行</Description>
      <Maximum>1</Maximum>
      <SortID>78100</SortID>
    </Field>
	<Field Def="u8 isDestinedDeathHpMult:1">
	  <Enum>SP_EFFECT_BOOL</Enum>
	  <Maximum>1</Maximum>
	</Field>
    
	<Field Def="u8 padbit_old:3" RemovedVersion="11210015" />
    
	<Field Def="u8 isHpBurnEffect:1" FirstVersion="11210015">
	  <Enum>SP_EFFECT_BOOL</Enum>
	  <Maximum>1</Maximum>
	</Field>
	<Field Def="u8 unknown_0x352_6:1" FirstVersion="11210015">
	  <Enum>SP_EFFECT_BOOL</Enum>
	  <Maximum>1</Maximum>
	</Field>
	<Field Def="u8 unknown_0x352_7:1" FirstVersion="11210015">
	  <Enum>SP_EFFECT_BOOL</Enum>
	  <Maximum>1</Maximum>
	</Field>
    
    <Field Def="dummy8 pad2[1]" RemovedVersion="11210015" >
      <DisplayName>pad</DisplayName>
      <SortID>470001</SortID>
    </Field>
    
    <Field Def="u8 unknown_0x353_0:1" FirstVersion="11210015" />
    <Field Def="u8 unknown_0x353_1:1" FirstVersion="11210015" />
    <Field Def="u8 unknown_0x353_2:1" FirstVersion="11210015" />
    <Field Def="u8 unknown_0x353_3:1" FirstVersion="11210015" />
    <Field Def="u8 unknown_0x353_4:1" FirstVersion="11210015" />
    <Field Def="dummy8 unknown_0x353_5:3" FirstVersion="11210015" />
    
    <Field Def="f32 changeSuperArmorPoint">
      <DisplayName>最大SA加算値[point]</DisplayName>
      <Description>スーパーアーマー値に加算する値</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>58100</SortID>
    </Field>
    <Field Def="f32 changeSaPoint">
      <DisplayName>SAダメージ量[point]</DisplayName>
      <Description>一度の発動で何ポイント減算（または加算）するかを設定</Description>
      <Minimum>-9999</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>58200</SortID>
    </Field>
    <Field Def="f32 hugeEnemyPickupHeightOverwrite">
      <DisplayName>巨大敵持ち上げ高さ上書き[m]</DisplayName>
      <Description>巨大敵持ち上げ高さ上書き[m]</Description>
      <Minimum>0</Minimum>
      <Maximum>99.9</Maximum>
      <Increment>0.1</Increment>
      <SortID>470000</SortID>
    </Field>
    <Field Def="f32 poisonDefDamageRate = 1">
      <DisplayName>防御側：毒耐性ダメージ倍率</DisplayName>
      <Description>毒耐性ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>31300</SortID>
    </Field>
    <Field Def="f32 diseaseDefDamageRate = 1">
      <DisplayName>防御側：疫病耐性ダメージ倍率</DisplayName>
      <Description>疫病耐性ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>31400</SortID>
    </Field>
    <Field Def="f32 bloodDefDamageRate = 1">
      <DisplayName>防御側：出血耐性ダメージ倍率</DisplayName>
      <Description>出血耐性ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>31500</SortID>
    </Field>
    <Field Def="f32 curseDefDamageRate = 1">
      <DisplayName>防御側：呪耐性ダメージ倍率</DisplayName>
      <Description>呪耐性ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>31600</SortID>
    </Field>
    <Field Def="f32 freezeDefDamageRate = 1">
      <DisplayName>防御側：冷気耐性ダメージ倍率</DisplayName>
      <Description>冷気耐性ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>31700</SortID>
    </Field>
    <Field Def="f32 sleepDefDamageRate = 1">
      <DisplayName>防御側：睡眠耐性ダメージ倍率</DisplayName>
      <Description>睡眠耐性ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>31800</SortID>
    </Field>
    <Field Def="f32 madnessDefDamageRate = 1">
      <DisplayName>防御側：発狂耐性ダメージ倍率</DisplayName>
      <Description>発狂耐性ダメージ倍率：算出したダメージに×○倍で補正をかける。１が通常。</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.001</Increment>
      <SortID>31900</SortID>
    </Field>
    <Field Def="u16 overwrite_maxBackhomeDist">
      <DisplayName>何があっても帰宅する距離[m]_上書き</DisplayName>
      <Description>何があっても帰宅する距離[m]_上書き</Description>
      <SortID>74000</SortID>
    </Field>
    <Field Def="u16 overwrite_backhomeDist">
      <DisplayName>戦闘しつつ帰宅する距離[m]_上書き</DisplayName>
      <Description>戦闘しつつ帰宅する距離[m]_上書き</Description>
      <SortID>74010</SortID>
    </Field>
    <Field Def="u16 overwrite_backhomeBattleDist">
      <DisplayName>巣に帰るのをあきらめて戦闘する距離[m]_上書き</DisplayName>
      <Description>巣に帰るのをあきらめて戦闘する距離[m]_上書き </Description>
      <SortID>74020</SortID>
    </Field>
    <Field Def="u16 overwrite_BackHome_LookTargetDist">
      <DisplayName>帰宅時：ターゲットを見ている距離[m]_上書き</DisplayName>
      <Description>帰宅時：ターゲットを見ている距離[m]_上書き</Description>
      <SortID>74030</SortID>
    </Field>
    <Field Def="f32 goodsConsumptionRate = 1">
      <DisplayName>アイテム消費MP倍率</DisplayName>
      <Description>アイテム消費MP倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>990</Maximum>
      <Increment>0.001</Increment>
      <SortID>300040</SortID>
    </Field>
    <Field Def="f32 guardStaminaMult = 1">
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%f</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>470002</SortID>
    </Field>
    
    <Field Def="s32 spiritDeathSpEffectId" FirstVersion="11210015" />
    
    <Field Def="dummy8 unk3[4]" RemovedVersion="11210015" >
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%f</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>470002</SortID>
    </Field>
  </Fields>
</PARAMDEF>