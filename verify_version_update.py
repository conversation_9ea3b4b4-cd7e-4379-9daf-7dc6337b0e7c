#!/usr/bin/env python3
"""
版本号更新验证脚本
验证所有相关文件中的版本号是否已正确更新到 3.0.3
"""

import os
import re
from pathlib import Path


def check_file_version(file_path: Path, expected_version: str, patterns: list) -> dict:
    """检查文件中的版本号"""
    result = {
        "file": str(file_path),
        "exists": file_path.exists(),
        "matches": [],
        "issues": []
    }
    
    if not file_path.exists():
        result["issues"].append("文件不存在")
        return result
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        for pattern_info in patterns:
            pattern = pattern_info["pattern"]
            description = pattern_info["description"]
            
            matches = re.findall(pattern, content)
            if matches:
                for match in matches:
                    version = match if isinstance(match, str) else match[0] if match else ""
                    if version == expected_version:
                        result["matches"].append({
                            "description": description,
                            "version": version,
                            "status": "✅ 正确"
                        })
                    else:
                        result["matches"].append({
                            "description": description,
                            "version": version,
                            "status": "❌ 需要更新"
                        })
                        result["issues"].append(f"{description}: 发现版本 {version}，应为 {expected_version}")
            else:
                result["issues"].append(f"{description}: 未找到版本号")
    
    except Exception as e:
        result["issues"].append(f"读取文件失败: {e}")
    
    return result


def verify_version_update():
    """验证版本号更新"""
    print("🔍 验证版本号更新到 3.0.3...")
    print("=" * 60)
    
    expected_version = "3.0.3"
    
    # 定义需要检查的文件和模式
    files_to_check = [
        {
            "path": Path("build_pyinstaller.py"),
            "patterns": [
                {
                    "pattern": r'self\.version = "([^"]+)"',
                    "description": "PyInstaller 打包脚本版本"
                }
            ]
        },
        {
            "path": Path("build_nuitka.py"),
            "patterns": [
                {
                    "pattern": r'self\.version = "([^"]+)"',
                    "description": "Nuitka 打包脚本版本"
                }
            ]
        },
        {
            "path": Path("src/app.py"),
            "patterns": [
                {
                    "pattern": r'setApplicationVersion\("([^"]+)"\)',
                    "description": "应用程序版本"
                }
            ]
        },
        {
            "path": Path("src/ui/main_window.py"),
            "patterns": [
                {
                    "pattern": r'self\.base_title = "Nmodm v([^"]+)"',
                    "description": "主窗口标题版本"
                }
            ]
        },
        {
            "path": Path("src/ui/pages/about_page.py"),
            "patterns": [
                {
                    "pattern": r'QGroupBox\("v([^"]+) 更新说明"\)',
                    "description": "关于页面更新说明版本"
                }
            ]
        }
    ]
    
    all_correct = True
    
    for file_info in files_to_check:
        file_path = file_info["path"]
        patterns = file_info["patterns"]
        
        print(f"📁 检查文件: {file_path}")
        
        result = check_file_version(file_path, expected_version, patterns)
        
        if not result["exists"]:
            print(f"   ❌ 文件不存在")
            all_correct = False
            continue
        
        if result["issues"]:
            print(f"   ❌ 发现问题:")
            for issue in result["issues"]:
                print(f"      • {issue}")
            all_correct = False
        else:
            print(f"   ✅ 版本号正确")
        
        for match in result["matches"]:
            print(f"      {match['status']} {match['description']}: {match['version']}")
        
        print()
    
    # 检查是否有遗漏的旧版本号
    print("🔍 扫描可能遗漏的旧版本号...")
    
    old_versions = ["3.0.2", "3.0.1", "3.0.0"]
    suspicious_files = []
    
    # 扫描主要源代码目录
    source_dirs = [Path("src"), Path(".")]
    
    for source_dir in source_dirs:
        if source_dir.exists():
            for file_path in source_dir.rglob("*.py"):
                try:
                    # 排除虚拟环境和其他无关目录
                    path_str = str(file_path)
                    if any(exclude in path_str for exclude in [".venv", "venv", "__pycache__", ".git"]):
                        continue

                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    for old_version in old_versions:
                        if old_version in content:
                            # 排除一些已知的无关文件
                            if file_path.name in ["verify_version_update.py"]:
                                continue

                            suspicious_files.append({
                                "file": str(file_path),
                                "old_version": old_version
                            })
                            break

                except:
                    continue
    
    if suspicious_files:
        print("⚠️ 发现可能包含旧版本号的文件:")
        for item in suspicious_files:
            print(f"   📄 {item['file']} (包含 {item['old_version']})")
        print()
    else:
        print("✅ 未发现遗漏的旧版本号")
        print()
    
    # 总结
    print("📋 版本更新验证结果:")
    print("=" * 60)
    
    if all_correct and not suspicious_files:
        print("🎉 版本号更新验证通过！")
        print("✅ 所有关键文件的版本号已正确更新到 3.0.3")
        print("✅ 未发现遗漏的旧版本号")
    else:
        print("❌ 版本号更新验证失败！")
        if not all_correct:
            print("❌ 部分文件的版本号未正确更新")
        if suspicious_files:
            print("❌ 发现可能包含旧版本号的文件")
        print("\n请检查上述问题并重新更新版本号。")
    
    print()
    print("📋 已检查的文件:")
    for file_info in files_to_check:
        status = "✅" if file_info["path"].exists() else "❌"
        print(f"   {status} {file_info['path']}")
    
    return all_correct and not suspicious_files


def show_version_info():
    """显示版本信息"""
    print("📋 Nmodm v3.0.3 版本信息:")
    print("=" * 60)
    print("🚀 主要更新内容:")
    print("• 网络优化配置完整集成到房间系统")
    print("• easytier_config.json 同步网络优化配置")
    print("• 房间加载保护机制增强")
    print("• 网络优化自动提权功能")
    print("• 配置管理体系完善")
    print()
    print("🔧 技术改进:")
    print("• 三层配置管理体系")
    print("• 智能权限处理")
    print("• 安全保护机制")
    print("• 用户体验优化")
    print()


if __name__ == "__main__":
    show_version_info()
    success = verify_version_update()
    
    if success:
        print("🎯 版本号更新完成，可以进行发布！")
    else:
        print("⚠️ 请修复版本号问题后再进行发布。")
