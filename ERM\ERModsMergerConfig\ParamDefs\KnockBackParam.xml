﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>KNOCKBACK_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 damage_Min_ContTime">
      <DisplayName>極小ダメージ_速度維持時間[s]</DisplayName>
      <Description>極小ダメージアニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="f32 damage_S_ContTime">
      <DisplayName>小ダメージ_速度維持時間[s]</DisplayName>
      <Description>小ダメージアニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="f32 damage_M_ContTime">
      <DisplayName>中ダメージ_速度維持時間[s]</DisplayName>
      <Description>中ダメージアニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="f32 damage_L_ContTime">
      <DisplayName>大ダメージ_速度維持時間[s]</DisplayName>
      <Description>大ダメージアニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="f32 damage_BlowS_ContTime">
      <DisplayName>小吹っ飛び_速度維持時間[s]</DisplayName>
      <Description>小吹っ飛びダメージアニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="f32 damage_BlowM_ContTime">
      <DisplayName>大吹っ飛び_速度維持時間[s]</DisplayName>
      <Description>大吹っ飛びダメージアニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>1100</SortID>
    </Field>
    <Field Def="f32 damage_Strike_ContTime">
      <DisplayName>叩きつけ_速度維持時間[s]</DisplayName>
      <Description>叩きつけダメージアニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>1300</SortID>
    </Field>
    <Field Def="f32 damage_Uppercut_ContTime">
      <DisplayName>打ち上げ_速度維持時間[s]</DisplayName>
      <Description>打ち上げダメージアニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>1500</SortID>
    </Field>
    <Field Def="f32 damage_Push_ContTime">
      <DisplayName>プッシュ_速度維持時間[s]</DisplayName>
      <Description>プッシュダメージアニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>1700</SortID>
    </Field>
    <Field Def="f32 damage_Breath_ContTime">
      <DisplayName>ブレス_速度維持時間[s]</DisplayName>
      <Description>ブレスダメージアニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>1810</SortID>
    </Field>
    <Field Def="f32 damage_HeadShot_ContTime">
      <DisplayName>ヘッドショット_速度維持時間[s]</DisplayName>
      <Description>ヘッドショットダメージアニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>1900</SortID>
    </Field>
    <Field Def="f32 guard_S_ContTime">
      <DisplayName>ガード受け小_速度維持時間[s]</DisplayName>
      <Description>ガード受け小アニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="f32 guard_L_ContTime">
      <DisplayName>ガード受け大_速度維持時間[s]</DisplayName>
      <Description>ガード受け大アニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>2300</SortID>
    </Field>
    <Field Def="f32 guard_LL_ContTime">
      <DisplayName>ガード受け特大_速度維持時間[s]</DisplayName>
      <Description>ガード受け特大アニメの時に使用される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>2410</SortID>
    </Field>
    <Field Def="f32 guardBrake_ContTime">
      <DisplayName>ガードくずされ_速度維持時間[s]</DisplayName>
      <Description>ガードくずされアニメの時に仕様される維持時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>2500</SortID>
    </Field>
    <Field Def="f32 damage_Min_DecTime">
      <DisplayName>極小ダメージ_減速時間[s]</DisplayName>
      <Description>極小ダメージアニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="f32 damage_S_DecTime">
      <DisplayName>小ダメージ_減速時間[s]</DisplayName>
      <Description>小ダメージアニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="f32 damage_M_DecTime">
      <DisplayName>中ダメージ_減速時間[s]</DisplayName>
      <Description>中ダメージアニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>600</SortID>
    </Field>
    <Field Def="f32 damage_L_DecTime">
      <DisplayName>大ダメージ_減速時間[s]</DisplayName>
      <Description>大ダメージアニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="f32 damage_BlowS_DecTime">
      <DisplayName>小吹っ飛び_減速時間[s]</DisplayName>
      <Description>小吹っ飛びダメージアニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="f32 damage_BlowM_DecTime">
      <DisplayName>大吹っ飛び_減速時間[s]</DisplayName>
      <Description>大吹っ飛びダメージアニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="f32 damage_Strike_DecTime">
      <DisplayName>叩きつけ_減速時間[s]</DisplayName>
      <Description>叩きつけダメージアニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>1400</SortID>
    </Field>
    <Field Def="f32 damage_Uppercut_DecTime">
      <DisplayName>打ち上げ_減速時間[s]</DisplayName>
      <Description>打ち上げダメージアニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>1600</SortID>
    </Field>
    <Field Def="f32 damage_Push_DecTime">
      <DisplayName>プッシュ_減速時間[s]</DisplayName>
      <Description>プッシュダメージアニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>1800</SortID>
    </Field>
    <Field Def="f32 damage_Breath_DecTime">
      <DisplayName>ブレス_減速時間[s]</DisplayName>
      <Description>ブレスダメージアニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>1820</SortID>
    </Field>
    <Field Def="f32 damage_HeadShot_DecTime">
      <DisplayName>ヘッドショット_減速時間[s]</DisplayName>
      <Description>ヘッドショットダメージアニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="f32 guard_S_DecTime">
      <DisplayName>ガード受け小_減速時間[s]</DisplayName>
      <Description>ガード受け小アニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>2200</SortID>
    </Field>
    <Field Def="f32 guard_L_DecTime">
      <DisplayName>ガード受け大_減速時間[s]</DisplayName>
      <Description>ガード受け大アニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>2400</SortID>
    </Field>
    <Field Def="f32 guard_LL_DecTime">
      <DisplayName>ガード受け特大_減速時間[s]</DisplayName>
      <Description>ガード受け特大アニメの時に使用される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>2420</SortID>
    </Field>
    <Field Def="f32 guardBrake_DecTime">
      <DisplayName>ガードくずされ_減速時間[s]</DisplayName>
      <Description>ガードくずされアニメの時に仕様される減速時間を設定</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9.99</Maximum>
      <SortID>2600</SortID>
    </Field>
    <Field Def="dummy8 pad[8]">
      <DisplayName>pading</DisplayName>
      <SortID>2601</SortID>
    </Field>
  </Fields>
</PARAMDEF>