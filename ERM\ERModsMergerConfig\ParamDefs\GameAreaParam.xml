﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>GAME_AREA_PARAM_ST</ParamType>
  <DataVersion>3</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 disableParam_NT:1">
      <DisplayName>NT版出力から外すか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータをNT版パッケージでは除外します</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
    </Field>
    <Field Def="dummy8 disableParamReserve1:7">
      <DisplayName>パッケージ出力用リザーブ1</DisplayName>
      <Description>パッケージ出力用リザーブ1</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>1000000</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve2[3]">
      <DisplayName>パッケージ出力用リザーブ2</DisplayName>
      <Description>パッケージ出力用リザーブ2</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>1000001</SortID>
    </Field>
    <Field Def="u32 bonusSoul_single">
      <DisplayName>シングル時クリアボーナスソウル量</DisplayName>
      <Description>エリアボスを倒したときに取得できるソウル量(シングルプレイ時)</Description>
      <Maximum>99999999</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="u32 bonusSoul_multi">
      <DisplayName>マルチプレイ時クリアボーナスソウル量</DisplayName>
      <Description>エリアボスを倒したときに取得できるソウル量(マルチプレイ時)</Description>
      <Maximum>99999999</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="u32 humanityPointCountFlagIdTop">
      <DisplayName>人間性ドロップポイントカウント先頭フラグID</DisplayName>
      <Description>人間性ドロップポイントを管理する為の先頭フラグID(20Bit使用)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="u16 humanityDropPoint1">
      <DisplayName>人間性ドロップ必要ポイント1</DisplayName>
      <Description>人間性を取得する為の閾値1</Description>
      <Maximum>65000</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="u16 humanityDropPoint2">
      <DisplayName>人間性ドロップ必要ポイント2</DisplayName>
      <Description>人間性を取得する為の閾値2</Description>
      <Maximum>65000</Maximum>
      <SortID>401</SortID>
    </Field>
    <Field Def="u16 humanityDropPoint3">
      <DisplayName>人間性ドロップ必要ポイント3</DisplayName>
      <Description>人間性を取得する為の閾値3</Description>
      <Maximum>65000</Maximum>
      <SortID>402</SortID>
    </Field>
    <Field Def="u16 humanityDropPoint4">
      <DisplayName>人間性ドロップ必要ポイント4</DisplayName>
      <Description>人間性を取得する為の閾値4</Description>
      <Maximum>65000</Maximum>
      <SortID>403</SortID>
    </Field>
    <Field Def="u16 humanityDropPoint5">
      <DisplayName>人間性ドロップ必要ポイント5</DisplayName>
      <Description>人間性を取得する為の閾値5</Description>
      <Maximum>65000</Maximum>
      <SortID>404</SortID>
    </Field>
    <Field Def="u16 humanityDropPoint6">
      <DisplayName>人間性ドロップ必要ポイント6</DisplayName>
      <Description>人間性を取得する為の閾値6</Description>
      <Maximum>65000</Maximum>
      <SortID>405</SortID>
    </Field>
    <Field Def="u16 humanityDropPoint7">
      <DisplayName>人間性ドロップ必要ポイント7</DisplayName>
      <Description>人間性を取得する為の閾値7</Description>
      <Maximum>65000</Maximum>
      <SortID>406</SortID>
    </Field>
    <Field Def="u16 humanityDropPoint8">
      <DisplayName>人間性ドロップ必要ポイント8</DisplayName>
      <Description>人間性を取得する為の閾値8</Description>
      <Maximum>65000</Maximum>
      <SortID>407</SortID>
    </Field>
    <Field Def="u16 humanityDropPoint9">
      <DisplayName>人間性ドロップ必要ポイント9</DisplayName>
      <Description>人間性を取得する為の閾値9</Description>
      <Maximum>65000</Maximum>
      <SortID>408</SortID>
    </Field>
    <Field Def="u16 humanityDropPoint10">
      <DisplayName>人間性ドロップ必要ポイント10</DisplayName>
      <Description>人間性を取得する為の閾値10</Description>
      <Maximum>65000</Maximum>
      <SortID>409</SortID>
    </Field>
    <Field Def="u32 soloBreakInPoint_Min">
      <DisplayName>ソロ侵入ポイント加算値下限</DisplayName>
      <Description>エリアボスを倒したときに加算するソロ侵入ポイントの最小値。</Description>
      <Maximum>999999999</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="u32 soloBreakInPoint_Max = 10000">
      <DisplayName>ソロ侵入ポイント加算値上限</DisplayName>
      <Description>エリアボスを倒したときに加算するソロ侵入ポイントの最大値。</Description>
      <Maximum>999999999</Maximum>
      <SortID>510</SortID>
    </Field>
    <Field Def="u32 defeatBossFlagId_forSignAimList">
      <DisplayName>ボス撃破済みフラグID(ホスト化時の目的表示用)</DisplayName>
      <Description>このフラグがONの場合はホスト化時の目的設定のリストに表示しない。0の場合は常時表示。</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>999999</SortID>
    </Field>
    <Field Def="u32 displayAimFlagId">
      <DisplayName>目的表示フラグID</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>999999</SortID>
    </Field>
    <Field Def="u32 foundBossFlagId">
      <DisplayName>ボス発見フラグID</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>999999</SortID>
    </Field>
    <Field Def="s32 foundBossTextId = -1">
      <DisplayName>発見時テキストID</DisplayName>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>999999</SortID>
    </Field>
    <Field Def="s32 notFindBossTextId = -1">
      <DisplayName>未発見時テキストID</DisplayName>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>999999</SortID>
    </Field>
    <Field Def="u32 bossChallengeFlagId">
      <DisplayName>ボス挑戦可能フラグID</DisplayName>
      <Description>ボス挑戦可能フラグID。マルチプレイエリアパラの「侵入ポイント自動生成か」が○のときの侵入位置検索で目的のボスを選ぶ時にこのフラグがONのボスが対象になる。0の場合は常に対象になる。</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>600</SortID>
    </Field>
    <Field Def="u32 defeatBossFlagId">
      <DisplayName>ボス撃破フラグID</DisplayName>
      <Description>ボス撃破フラグID。マルチプレイエリアパラの「侵入ポイント自動生成か」が○のときの侵入位置検索で目的のボスを選ぶ時にこのフラグがOFFのボスが対象になる。</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>610</SortID>
    </Field>
    <Field Def="f32 bossPosX">
      <DisplayName>ボス位置_X座標</DisplayName>
      <Description>ボス位置_X座標（指定したマップからの相対座標）。マルチプレイエリアパラの「侵入ポイント自動生成か」が○のときの侵入位置検索でホストとボス間の距離確認に使われる。</Description>
      <Minimum>-20000</Minimum>
      <Maximum>20000</Maximum>
      <SortID>630</SortID>
    </Field>
    <Field Def="f32 bossPosY">
      <DisplayName>ボス位置_Y座標</DisplayName>
      <Description>ボス位置_Y座標（指定したマップからの相対座標）。マルチプレイエリアパラの「侵入ポイント自動生成か」が○のときの侵入位置検索でホストとボス間の距離確認に使われる。</Description>
      <Minimum>-20000</Minimum>
      <Maximum>20000</Maximum>
      <SortID>631</SortID>
    </Field>
    <Field Def="f32 bossPosZ">
      <DisplayName>ボス位置_Z座標</DisplayName>
      <Description>ボス位置_Z座標（指定したマップからの相対座標）。マルチプレイエリアパラの「侵入ポイント自動生成か」が○のときの侵入位置検索でホストとボス間の距離確認に使われる。</Description>
      <Minimum>-20000</Minimum>
      <Maximum>20000</Maximum>
      <SortID>632</SortID>
    </Field>
    <Field Def="u8 bossMapAreaNo">
      <DisplayName>ボス位置_エリア番号(mXX_00_00_00)</DisplayName>
      <Description>ボス位置_エリア番号(mXX_00_00_00)。マルチプレイエリアパラの「侵入ポイント自動生成か」が○のときの侵入位置検索でホストとボス間の距離確認に使われる。</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>99</Maximum>
      <SortID>620</SortID>
    </Field>
    <Field Def="u8 bossMapBlockNo">
      <DisplayName>ボス位置_グリッドX番号(m00_XX_00_00)</DisplayName>
      <Description>ボス位置_グリッドX番号(m00_XX_00_00)。マルチプレイエリアパラの「侵入ポイント自動生成か」が○のときの侵入位置検索でホストとボス間の距離確認に使われる。</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>99</Maximum>
      <SortID>621</SortID>
    </Field>
    <Field Def="u8 bossMapMapNo">
      <DisplayName>ボス位置_グリッドZ番号(m00_00_XX_00)</DisplayName>
      <Description>ボス位置_グリッドZ番号(m00_00_XX_00)。マルチプレイエリアパラの「侵入ポイント自動生成か」が○のときの侵入位置検索でホストとボス間の距離確認に使われる。</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>99</Maximum>
      <SortID>622</SortID>
    </Field>
    <Field Def="dummy8 reserve[9]">
      <DisplayName>予約領域</DisplayName>
      <Description>予約領域</Description>
      <SortID>1000002</SortID>
    </Field>
  </Fields>
</PARAMDEF>