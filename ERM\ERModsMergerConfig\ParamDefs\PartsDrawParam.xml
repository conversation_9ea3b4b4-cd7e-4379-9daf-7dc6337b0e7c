﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>PARTS_DRAW_PARAM_ST</ParamType>
  <DataVersion>5</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 lv01_BorderDist = 5">
      <DisplayName>LODレベル0-1境界距離[m]</DisplayName>
      <Description>切り替わる中心</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>100</SortID>
    </Field>
    <Field Def="f32 lv01_PlayDist = 1">
      <DisplayName>LODレベル0-1遊び距離[m]</DisplayName>
      <Description>境界中心で±遊び</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>20</Maximum>
      <Increment>0.1</Increment>
      <SortID>110</SortID>
    </Field>
    <Field Def="f32 lv12_BorderDist = 20">
      <DisplayName>LODレベル1-2境界距離[m]</DisplayName>
      <Description>切り替わる中心</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>200</SortID>
    </Field>
    <Field Def="f32 lv12_PlayDist = 2">
      <DisplayName>LODレベル1-2遊び距離[m]</DisplayName>
      <Description>境界中心で±遊び</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>40</Maximum>
      <Increment>0.1</Increment>
      <SortID>210</SortID>
    </Field>
    <Field Def="f32 lv23_BorderDist = 30">
      <DisplayName>LODレベル2-3境界距離[m]</DisplayName>
      <Description>切り替わる中心</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>300</SortID>
    </Field>
    <Field Def="f32 lv23_PlayDist">
      <DisplayName>LODレベル2-3遊び距離[m]</DisplayName>
      <Description>境界中心で±遊び</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>40</Maximum>
      <Increment>0.1</Increment>
      <SortID>310</SortID>
    </Field>
    <Field Def="f32 lv34_BorderDist = 9999">
      <DisplayName>LODレベル3-4境界距離[m]</DisplayName>
      <Description>切り替わる中心</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>400</SortID>
    </Field>
    <Field Def="f32 lv34_PlayDist">
      <DisplayName>LODレベル3-4遊び距離[m]</DisplayName>
      <Description>境界中心で±遊び</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>160</Maximum>
      <Increment>0.1</Increment>
      <SortID>410</SortID>
    </Field>
    <Field Def="f32 lv45_BorderDist = 9999">
      <DisplayName>LODレベル4-5境界距離[m]</DisplayName>
      <Description>切り替わる中心</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>500</SortID>
    </Field>
    <Field Def="f32 lv45_PlayDist">
      <DisplayName>LODレベル4-5遊び距離[m]</DisplayName>
      <Description>境界中心で±遊び</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>160</Maximum>
      <Increment>0.1</Increment>
      <SortID>510</SortID>
    </Field>
    <Field Def="f32 tex_lv01_BorderDist = 30">
      <DisplayName>TextureLODレベル0-1境界距離[m]</DisplayName>
      <Description>Texture切り替わる中心(0でTexureLOD無効)</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>1000</SortID>
    </Field>
    <Field Def="f32 tex_lv01_PlayDist = 1">
      <DisplayName>TextureLODレベル0-1遊び距離[m]</DisplayName>
      <Description>Texture境界中心で±遊び</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>20</Maximum>
      <Increment>0.1</Increment>
      <SortID>1100</SortID>
    </Field>
    <Field Def="u32 enableCrossFade:1">
      <DisplayName>クロスフェード有効</DisplayName>
      <Description>クロスフェード有効か(0:無効,1:有効)</Description>
      <Maximum>1</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="f32 drawDist = 9999">
      <DisplayName>描画距離[m]</DisplayName>
      <Description>描画最大距離。オープンではアクティベート距離に利用されます</Description>
      <EditFlags>None</EditFlags>
      <Minimum>1</Minimum>
      <Maximum>9999</Maximum>
      <Increment>1</Increment>
      <SortID>1300</SortID>
    </Field>
    <Field Def="f32 drawFadeRange">
      <DisplayName>フェード範囲[m]</DisplayName>
      <Description>描画最大距離から、実際に消えるまでのフェード距離</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>1400</SortID>
    </Field>
    <Field Def="f32 shadowDrawDist = 9999">
      <DisplayName>影描画距離[m]</DisplayName>
      <Description>影の描画最大距離</Description>
      <EditFlags>None</EditFlags>
      <Minimum>1</Minimum>
      <Maximum>9999</Maximum>
      <Increment>1</Increment>
      <SortID>1500</SortID>
    </Field>
    <Field Def="f32 shadowFadeRange">
      <DisplayName>影フェード範囲[m]</DisplayName>
      <Description>影の描画最大距離から、実際に消えるまでのフェード距離</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>1600</SortID>
    </Field>
    <Field Def="f32 motionBlur_BorderDist = 20">
      <DisplayName>モーションブラー描画境界距離[m]</DisplayName>
      <Description>モーションブラーが有効になる距離</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>1700</SortID>
    </Field>
    <Field Def="s8 isPointLightShadowSrc">
      <DisplayName>点光源の影を落とす</DisplayName>
      <Description>点光源の影を落とす</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>1800</SortID>
    </Field>
    <Field Def="s8 isDirLightShadowSrc">
      <DisplayName>平行光源の影を落とす</DisplayName>
      <Description>平行光源の影を落とす</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>1900</SortID>
    </Field>
    <Field Def="s8 isShadowDst">
      <DisplayName>影を受ける</DisplayName>
      <Description>影を受ける</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="s8 isShadowOnly">
      <DisplayName>影描画のみ</DisplayName>
      <Description>影描画のみ</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="s8 drawByReflectCam">
      <DisplayName>映り込む</DisplayName>
      <Description>映り込む</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>2200</SortID>
    </Field>
    <Field Def="s8 drawOnlyReflectCam">
      <DisplayName>映り込み描画のみ</DisplayName>
      <Description>映り込み描画のみ</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>2300</SortID>
    </Field>
    <Field Def="s8 IncludeLodMapLv = -1">
      <DisplayName>どのレベルのLodMapまで含めるか</DisplayName>
      <Description>どのレベルのLodMapまで含めるか</Description>
      <Minimum>-1</Minimum>
      <Maximum>16</Maximum>
      <SortID>2400</SortID>
    </Field>
    <Field Def="u8 isNoFarClipDraw">
      <DisplayName>FarClipしない</DisplayName>
      <Description>ファークリップを無効にし、常にクリップ空間の一番奥の深度に描画する。主に天球用</Description>
      <Maximum>1</Maximum>
      <SortID>2410</SortID>
    </Field>
    <Field Def="u8 lodType">
      <DisplayName>LODタイプ</DisplayName>
      <Enum>PARTS_DRAW_LOD_TYPE</Enum>
      <Description>LOD対象の種類、大きさ</Description>
      <EditFlags>None</EditFlags>
      <Maximum>64</Maximum>
      <SortID>2500</SortID>
    </Field>
    <Field Def="s8 shadowDrawLodOffset">
      <DisplayName>影描画LODレベルオフセット</DisplayName>
      <Description>影描画時のLODレベルオフセット値</Description>
      <Minimum>0</Minimum>
      <Maximum>5</Maximum>
      <SortID>1601</SortID>
    </Field>
    <Field Def="u8 isTraceCameraXZ">
      <DisplayName>カメラをXZ平面上で追従する</DisplayName>
      <Description>カメラをXZ平面上で追従する(GR SEQ09242)</Description>
      <Maximum>1</Maximum>
      <SortID>2420</SortID>
    </Field>
    <Field Def="u8 isSkydomeDrawPhase">
      <DisplayName>天球描画フェイズに切り替え</DisplayName>
      <Description>描画フェイズを天球に設定する(GR SEQ09242)</Description>
      <Maximum>1</Maximum>
      <SortID>2421</SortID>
    </Field>
    <Field Def="f32 DistantViewModel_BorderDist = 30">
      <DisplayName>遠景切り替え距離[m]</DisplayName>
      <Description>遠景切り替え距離[m]</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>1800</SortID>
    </Field>
    <Field Def="f32 DistantViewModel_PlayDist = 5">
      <DisplayName>遠景切り替え遊び距離[m]</DisplayName>
      <Description>遠景切り替え遊び距離[m]</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>1801</SortID>
    </Field>
    <Field Def="f32 LimtedActivate_BorderDist_forGrid = -1">
      <DisplayName>オープン用構築制限距離[m]</DisplayName>
      <Description>オープン用構築制限距離[m]。オープンにおいてカメラとの距離がこの距離未満だと構築されないようになります。遠景アセット用の機能です。-1:機能無効(デフォルト)</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999</Maximum>
      <Increment>0.1</Increment>
      <SortID>1802</SortID>
    </Field>
    <Field Def="f32 LimtedActivate_PlayDist_forGrid = 10">
      <DisplayName>オープン用構築制限遊び距離[m]</DisplayName>
      <Description>オープン構築制限遊び距離[m]</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>0.1</Increment>
      <SortID>1803</SortID>
    </Field>
    <Field Def="f32 zSortOffsetForNoFarClipDraw">
      <DisplayName>Zソートオフセット</DisplayName>
      <Description>同じ描画フェーズ内でカメラからの距離が同じ場合、半透明系は小さいほうが手前、不透明系は値が大きいほうが手前に描画されます。 オフセットの基点は天球描画フェーズのものは原点。それ以外はModelAABB中心。(GR SEQ09242)</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-99999</Minimum>
      <Maximum>99999</Maximum>
      <Increment>0.1</Increment>
      <SortID>2411</SortID>
    </Field>
    <Field Def="f32 shadowDrawAlphaTestDist = 9999">
      <DisplayName>影描画アルファテスト有効距離[m]</DisplayName>
      <Description>影描画時にアルファテストを行う距離[m]</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>1</Increment>
      <SortID>1602</SortID>
    </Field>
    <Field Def="u8 fowardDrawEnvmapBlendType">
      <DisplayName>Forward描画物の環境マップブレンドタイプ</DisplayName>
      <Enum>PARTS_DRAW_FOWARD_DRAW_ENVMAP_BLEND_TYPE</Enum>
      <Description>Forward描画物の環境マップブレンドタイプ</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>2430</SortID>
    </Field>
    <Field Def="u8 LBDrawDistScaleParamID">
      <DisplayName>描画距離スケールパラメータID</DisplayName>
      <Description>ロードバランサー描画距離スケールパラメータID</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>47</Maximum>
      <SortID>2501</SortID>
    </Field>
    <Field Def="dummy8 resereve[34]">
      <DisplayName>予約</DisplayName>
      <Description>予約</Description>
      <SortID>2502</SortID>
    </Field>
  </Fields>
</PARAMDEF>