#!/usr/bin/env python3
"""
OnlineFix文件夹解压逻辑测试脚本
测试ESL和Tool工具的新解压逻辑
"""

import sys
import os
import tempfile
import zipfile
import shutil
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def create_test_esl_zip(zip_path: Path):
    """创建测试用的ESL压缩包"""
    try:
        # 创建临时目录结构
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 创建ESL目录结构
            esl_dir = temp_path / "ESL"
            esl_dir.mkdir()
            
            steam_settings_dir = esl_dir / "steam_settings"
            steam_settings_dir.mkdir()
            
            # 创建必要的文件
            (esl_dir / "steamclient_loader.exe").write_text("fake steamclient_loader")
            (esl_dir / "ColdClientLoader.ini").write_text("[ColdClientLoader]\nAppId=1245620")
            (steam_settings_dir / "configs.user.ini").write_text("[user]\naccount_name=test\naccount_steamid=123456")
            
            # 创建压缩包
            with zipfile.ZipFile(zip_path, 'w') as zip_ref:
                for file_path in esl_dir.rglob('*'):
                    if file_path.is_file():
                        # 保持相对路径结构
                        arcname = file_path.relative_to(temp_path)
                        zip_ref.write(file_path, arcname)
            
            print(f"✅ 创建测试ESL压缩包: {zip_path}")
            return True
            
    except Exception as e:
        print(f"❌ 创建测试ESL压缩包失败: {e}")
        return False


def create_test_tool_zip(zip_path: Path):
    """创建测试用的Tool压缩包"""
    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 创建工具文件
            tools = [
                "WinIPBroadcast.exe",
                "client_windows_amd64.exe", 
                "server_windows_amd64.exe",
                "MicrosoftEdgeWebview2Setup.exe"
            ]
            
            for tool in tools:
                (temp_path / tool).write_text(f"fake {tool}")
            
            # 创建压缩包
            with zipfile.ZipFile(zip_path, 'w') as zip_ref:
                for tool in tools:
                    zip_ref.write(temp_path / tool, tool)
            
            print(f"✅ 创建测试Tool压缩包: {zip_path}")
            return True
            
    except Exception as e:
        print(f"❌ 创建测试Tool压缩包失败: {e}")
        return False


def test_esl_extraction_logic():
    """测试ESL解压逻辑"""
    print("🧪 测试ESL解压逻辑...")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 模拟项目结构
        root_dir = temp_path
        onlinefix_dir = root_dir / "OnlineFix"
        esl_dir = root_dir / "ESL"
        
        onlinefix_dir.mkdir()
        esl_dir.mkdir()
        
        # 创建测试压缩包
        esl_zip_path = onlinefix_dir / "esl2.zip"
        if not create_test_esl_zip(esl_zip_path):
            return False
        
        print("1. 测试初始状态检查...")
        
        # 模拟LanGamingPage的路径设置
        steamclient_dir = esl_dir
        steam_settings_dir = steamclient_dir / "steam_settings"
        esl_extracted_flag = steamclient_dir / ".esl_extracted"
        
        # 检查初始状态
        print(f"   OnlineFix/esl2.zip 存在: {esl_zip_path.exists()}")
        print(f"   ESL解压标志存在: {esl_extracted_flag.exists()}")
        print(f"   ESL目录结构完整: {(steamclient_dir / 'steamclient_loader.exe').exists()}")
        print()
        
        print("2. 测试解压过程...")
        
        # 模拟解压过程
        try:
            with zipfile.ZipFile(esl_zip_path, 'r') as zip_ref:
                zip_ref.extractall(root_dir)
            
            # 创建解压标志
            import time
            esl_extracted_flag.write_text(f"ESL extracted at {time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            print("   ✅ ESL解压完成")
            print("   ✅ 解压标志已创建")
            
        except Exception as e:
            print(f"   ❌ 解压失败: {e}")
            return False
        
        print()
        
        print("3. 测试解压后状态...")
        
        # 验证解压结果
        required_files = [
            steamclient_dir / "steamclient_loader.exe",
            steam_settings_dir,
        ]
        
        all_exist = all(path.exists() for path in required_files)
        print(f"   ESL文件结构完整: {all_exist}")
        print(f"   解压标志存在: {esl_extracted_flag.exists()}")
        print(f"   原压缩包保留: {esl_zip_path.exists()}")
        
        if all_exist and esl_extracted_flag.exists() and esl_zip_path.exists():
            print("   ✅ ESL解压逻辑测试通过")
            return True
        else:
            print("   ❌ ESL解压逻辑测试失败")
            return False


def test_tool_extraction_logic():
    """测试Tool解压逻辑"""
    print("🧪 测试Tool解压逻辑...")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 模拟项目结构
        root_dir = temp_path
        onlinefix_dir = root_dir / "OnlineFix"
        esr_dir = root_dir / "ESR"
        tool_dir = esr_dir / "tool"
        
        onlinefix_dir.mkdir()
        esr_dir.mkdir()
        tool_dir.mkdir(parents=True)
        
        # 创建测试压缩包
        tool_zip_path = onlinefix_dir / "tool.zip"
        if not create_test_tool_zip(tool_zip_path):
            return False
        
        print("1. 测试初始状态检查...")
        
        # 模拟ToolManager的路径设置
        tool_extracted_flag = tool_dir / ".tool_extracted"
        
        required_tools = {
            "WinIPBroadcast.exe": "IP广播工具",
            "MicrosoftEdgeWebview2Setup.exe": "WebView2安装程序"
            # KCP工具已移除，因为EasyTier自带KCP支持
        }
        
        # 检查初始状态
        print(f"   OnlineFix/tool.zip 存在: {tool_zip_path.exists()}")
        print(f"   Tool解压标志存在: {tool_extracted_flag.exists()}")
        
        missing_tools = []
        for tool_file in required_tools.keys():
            tool_path = tool_dir / tool_file
            if not tool_path.exists():
                missing_tools.append(tool_file)
        
        print(f"   缺失工具: {missing_tools if missing_tools else '无'}")
        print()
        
        print("2. 测试解压过程...")
        
        # 模拟解压过程
        try:
            with zipfile.ZipFile(tool_zip_path, 'r') as zip_ref:
                for file_info in zip_ref.infolist():
                    if file_info.is_dir():
                        continue
                    
                    filename = Path(file_info.filename).name
                    if filename in required_tools:
                        target_path = tool_dir / filename
                        
                        with zip_ref.open(file_info) as source, open(target_path, 'wb') as target:
                            shutil.copyfileobj(source, target)
                        
                        print(f"   ✅ 解压: {filename}")
            
            # 创建解压标志
            import time
            tool_extracted_flag.write_text(f"Tools extracted at {time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            print("   ✅ Tool解压完成")
            print("   ✅ 解压标志已创建")
            
        except Exception as e:
            print(f"   ❌ 解压失败: {e}")
            return False
        
        print()
        
        print("3. 测试解压后状态...")
        
        # 验证解压结果
        missing_tools_after = []
        for tool_file in required_tools.keys():
            tool_path = tool_dir / tool_file
            if not tool_path.exists():
                missing_tools_after.append(tool_file)
        
        print(f"   工具完整性: {'完整' if not missing_tools_after else f'缺失 {missing_tools_after}'}")
        print(f"   解压标志存在: {tool_extracted_flag.exists()}")
        print(f"   原压缩包保留: {tool_zip_path.exists()}")
        
        if not missing_tools_after and tool_extracted_flag.exists() and tool_zip_path.exists():
            print("   ✅ Tool解压逻辑测试通过")
            return True
        else:
            print("   ❌ Tool解压逻辑测试失败")
            return False


def test_migration_logic():
    """测试迁移逻辑（向后兼容）"""
    print("🧪 测试迁移逻辑...")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 模拟旧版本结构
        root_dir = temp_path
        esl_dir = root_dir / "ESL"
        esr_dir = root_dir / "ESR"
        onlinefix_dir = root_dir / "OnlineFix"
        
        esl_dir.mkdir()
        esr_dir.mkdir()
        onlinefix_dir.mkdir()
        
        # 创建旧位置的压缩包
        old_esl_zip = esl_dir / "esl2.zip"
        old_tool_zip = esr_dir / "tool.zip"
        
        create_test_esl_zip(old_esl_zip)
        create_test_tool_zip(old_tool_zip)
        
        print("1. 测试ESL迁移...")
        
        # 模拟ESL迁移
        new_esl_zip = onlinefix_dir / "esl2.zip"
        if old_esl_zip.exists():
            if new_esl_zip.exists():
                new_esl_zip.unlink()
            shutil.move(str(old_esl_zip), str(new_esl_zip))
            print(f"   ✅ ESL压缩包已迁移: {old_esl_zip} -> {new_esl_zip}")
        
        print("2. 测试Tool迁移...")
        
        # 模拟Tool迁移
        new_tool_zip = onlinefix_dir / "tool.zip"
        if old_tool_zip.exists():
            if new_tool_zip.exists():
                new_tool_zip.unlink()
            shutil.move(str(old_tool_zip), str(new_tool_zip))
            print(f"   ✅ Tool压缩包已迁移: {old_tool_zip} -> {new_tool_zip}")
        
        print("3. 验证迁移结果...")
        
        print(f"   新ESL位置存在: {new_esl_zip.exists()}")
        print(f"   新Tool位置存在: {new_tool_zip.exists()}")
        print(f"   旧ESL位置清空: {not old_esl_zip.exists()}")
        print(f"   旧Tool位置清空: {not old_tool_zip.exists()}")
        
        if new_esl_zip.exists() and new_tool_zip.exists() and not old_esl_zip.exists() and not old_tool_zip.exists():
            print("   ✅ 迁移逻辑测试通过")
            return True
        else:
            print("   ❌ 迁移逻辑测试失败")
            return False


def main():
    """主函数"""
    print("🎯 OnlineFix文件夹解压逻辑测试")
    print("=" * 60)
    
    results = []
    
    # 测试ESL解压逻辑
    results.append(test_esl_extraction_logic())
    print()
    
    # 测试Tool解压逻辑
    results.append(test_tool_extraction_logic())
    print()
    
    # 测试迁移逻辑
    results.append(test_migration_logic())
    print()
    
    # 总结
    print("📊 测试结果总结:")
    print("=" * 60)
    
    test_names = ["ESL解压逻辑", "Tool解压逻辑", "迁移逻辑"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    all_passed = all(results)
    print()
    
    if all_passed:
        print("🎉 所有测试通过！OnlineFix解压逻辑工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查实现逻辑。")
    
    print()
    print("💡 新的解压逻辑特性:")
    print("• 压缩包统一存放在OnlineFix文件夹")
    print("• 解压后保留原压缩包")
    print("• 添加解压完成标志文件")
    print("• 支持旧版本压缩包自动迁移")
    print("• 启动时优先检查解压标志")


if __name__ == "__main__":
    main()
