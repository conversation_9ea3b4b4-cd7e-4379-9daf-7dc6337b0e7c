﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>OBJECT_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>106</FormatVersion>
  <Fields>
    <Field Def="s16 hp = -1">
      <DisplayName>HP</DisplayName>
      <Description>破壊までの耐久力(-1:破壊不可)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="u16 defense">
      <DisplayName>防御力</DisplayName>
      <Description>この値以下の攻撃力はダメージなし</Description>
      <EditFlags>None</EditFlags>
      <Maximum>9999</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="s16 extRefTexId = -1">
      <DisplayName>外部参照テクスチャID</DisplayName>
      <Description>mAA/mAA_????.tpf(-1:なし)(AA:エリア番号)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="s16 materialId = -1">
      <DisplayName>材質ID</DisplayName>
      <Description>マテリアルID。床材質と同じ扱い。-1のときは今までと同じ挙動</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="u8 animBreakIdMax">
      <DisplayName>アニメ破壊ID最大値</DisplayName>
      <Description>アニメ破壊IDが0番から何番までか</Description>
      <EditFlags>None</EditFlags>
      <Maximum>99</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="u8 isCamHit:1">
      <DisplayName>カメラが当たるか</DisplayName>
      <Description>カメラが当たるか(0:当たらない, 1:当たる)</Description>
      <Maximum>1</Maximum>
      <SortID>310</SortID>
    </Field>
    <Field Def="u8 isBreakByPlayerCollide:1">
      <DisplayName>プレイヤ衝突で壊れるか</DisplayName>
      <Description>プレイヤが接触したときに壊れ(0:ない, 1:る)</Description>
      <Maximum>1</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="u8 isAnimBreak:1">
      <DisplayName>アニメ破壊か</DisplayName>
      <Description>アニメ破壊か(0:物理破壊, 1:アニメ破壊)</Description>
      <Maximum>1</Maximum>
      <SortID>600</SortID>
    </Field>
    <Field Def="u8 isPenetrationBulletHit:1">
      <DisplayName>貫通弾丸が当たるか</DisplayName>
      <Description>貫通弾丸が当たるか(0:当たらない, 1:当たる)</Description>
      <Maximum>1</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="u8 isChrHit:1 = 1">
      <DisplayName>キャラが当たるか</DisplayName>
      <Description>キャラが当たるか(0:当たらない, 1:当たる)</Description>
      <Maximum>1</Maximum>
      <SortID>311</SortID>
    </Field>
    <Field Def="u8 isAttackBacklash:1 = 1">
      <DisplayName>攻撃を弾くか</DisplayName>
      <Description>攻撃を弾くか(0:弾かない, 1:弾く)</Description>
      <Maximum>1</Maximum>
      <SortID>950</SortID>
    </Field>
    <Field Def="u8 isDisableBreakForFirstAppear:1">
      <DisplayName>初期出現用破壊禁止</DisplayName>
      <Description>プレイヤの初期出現で壊れ(0:る, 1:ない)</Description>
      <Maximum>1</Maximum>
      <SortID>450</SortID>
    </Field>
    <Field Def="u8 isLadder:1">
      <DisplayName>ハシゴか</DisplayName>
      <Description>ハシゴか(0:ちがう, 1:そう)</Description>
      <Maximum>1</Maximum>
      <SortID>970</SortID>
    </Field>
    <Field Def="u8 isAnimPauseOnRemoPlay:1">
      <DisplayName>ポリ劇中アニメを停止するか</DisplayName>
      <Description>ポリ劇中アニメを停止するか(0:しない, 1:する)</Description>
      <Maximum>1</Maximum>
      <SortID>980</SortID>
    </Field>
    <Field Def="u8 isDamageNoHit:1">
      <DisplayName>ダメージが当たらないか</DisplayName>
      <Description>ダメージが当たらない(0:当たる, 1:当たらない)</Description>
      <Maximum>1</Maximum>
      <SortID>312</SortID>
    </Field>
    <Field Def="u8 isMoveObj:1">
      <DisplayName>移動オブジェか</DisplayName>
      <Description>移動オブジェか(0:ちがう, 1:そう)</Description>
      <Maximum>1</Maximum>
      <SortID>975</SortID>
    </Field>
    <Field Def="u8 isRopeBridge:1">
      <DisplayName>吊り橋オブジェクトか</DisplayName>
      <Description>吊り橋オブジェクトか(0:ちがう, 1:そう)</Description>
      <Maximum>1</Maximum>
      <SortID>971</SortID>
    </Field>
    <Field Def="u8 isAddRigidImpulse_ByDamage:1">
      <DisplayName>ダメージによって剛体が吹き飛ぶか</DisplayName>
      <Description>ダメージによって剛体が吹き飛ぶか(0:吹き飛ばない, 1:吹き飛ぶ)</Description>
      <Maximum>1</Maximum>
      <SortID>376</SortID>
    </Field>
    <Field Def="u8 isBreak_ByChrRide:1">
      <DisplayName>キャラが乗ったら壊れるか</DisplayName>
      <Description>キャラが乗ったら壊れるか(0:壊れるない 1:壊れる)</Description>
      <Maximum>1</Maximum>
      <SortID>402</SortID>
    </Field>
    <Field Def="u8 isBurn:1">
      <DisplayName>燃焼するか</DisplayName>
      <Description>燃焼するか(0:しない, 1:する)</Description>
      <Maximum>1</Maximum>
      <SortID>1400</SortID>
    </Field>
    <Field Def="u8 isBreakByEnemyCollide:1">
      <DisplayName>敵キャラ衝突で壊れるか</DisplayName>
      <Description>敵キャラが接触したときに壊れ(0:ない, 1:る)</Description>
      <Maximum>1</Maximum>
      <SortID>401</SortID>
    </Field>
    <Field Def="s8 defaultLodParamId = -1">
      <DisplayName>デフォルトLODパラムID</DisplayName>
      <Description>デフォルトLODパラムID(-1：なし)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>1300</SortID>
    </Field>
    <Field Def="s32 breakSfxId = -1">
      <DisplayName>破壊時SFXID</DisplayName>
      <Description>オブジェ破壊時のSFXID(-1:デフォルト(810030))</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>1E+09</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="s32 breakSfxCpId = -1">
      <DisplayName>破壊時SFXダミポリID</DisplayName>
      <Description>オブジェ破壊時SFXの発生位置ダミポリID(-1：配置位置）</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>1001</SortID>
    </Field>
    <Field Def="s32 breakBulletBehaviorId = -1">
      <DisplayName>破壊時 弾発生 行動パラメータID</DisplayName>
      <Description>破壊時[弾]の行動パラメータ(-1:発生しない)</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>1050</SortID>
    </Field>
    <Field Def="s32 breakBulletCpId = -1">
      <DisplayName>破壊時 弾発生 ダミポリID</DisplayName>
      <Description>破壊時[弾]の発生位置ダミポリID(-1:配置位置)</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>1051</SortID>
    </Field>
    <Field Def="u8 breakFallHeight">
      <DisplayName>落下破壊高さ(m)</DisplayName>
      <Description>落下時にオブジェクトが壊れる高さ（0：落下では壊れない)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>1100</SortID>
    </Field>
    <Field Def="u8 windEffectType_0">
      <DisplayName>風影響タイプ(破壊前)</DisplayName>
      <Enum>OBJECT_WIND_EFFECT_TYPE</Enum>
      <Description>風影響タイプ(破壊前)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>2</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="u8 windEffectType_1">
      <DisplayName>風影響タイプ(破壊後)</DisplayName>
      <Enum>OBJECT_WIND_EFFECT_TYPE</Enum>
      <Description>風影響タイプ(破壊後)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>2</Maximum>
      <SortID>1201</SortID>
    </Field>
    <Field Def="u8 camAvoidType = 1">
      <DisplayName>カメラ回避設定</DisplayName>
      <Enum>OBJECT_CAM_AVOID_TYPE</Enum>
      <Description>オブジェクトがカメラ・プレイヤー間を遮蔽した場合の対処方法</Description>
      <EditFlags>None</EditFlags>
      <Maximum>2</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="f32 windEffectRate_0 = 0.5">
      <DisplayName>風係数(破壊前)</DisplayName>
      <Description>風係数(破壊前)</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1202</SortID>
    </Field>
    <Field Def="f32 windEffectRate_1 = 0.5">
      <DisplayName>風係数(破壊後)</DisplayName>
      <Description>風係数(破壊後)</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1203</SortID>
    </Field>
    <Field Def="f32 breakStopTime">
      <DisplayName>破壊後強制停止時間</DisplayName>
      <Description>破壊されてから剛体を強制的に停止するまでの時間（0で強制停止しない）</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1101</SortID>
    </Field>
    <Field Def="f32 burnTime">
      <DisplayName>燃焼時間(秒)</DisplayName>
      <Description>燃焼時間(秒)(0で燃え続ける)</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1401</SortID>
    </Field>
    <Field Def="f32 burnBraekRate = 0.5">
      <DisplayName>燃焼 破壊判定進行度</DisplayName>
      <Description>破壊状態に切り替わる燃焼度の閾値</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>1402</SortID>
    </Field>
    <Field Def="s32 burnSfxId = -1">
      <DisplayName>燃焼 SFXID：0</DisplayName>
      <Description>燃焼時のSFXID：0 (-1：SFXなし)</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>1410</SortID>
    </Field>
    <Field Def="s32 burnSfxId_1 = -1">
      <DisplayName>燃焼 SFXID：1</DisplayName>
      <Description>燃焼時のSFXID：1 (-1：SFXなし)</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>1411</SortID>
    </Field>
    <Field Def="s32 burnSfxId_2 = -1">
      <DisplayName>燃焼 SFXID：2</DisplayName>
      <Description>燃焼時のSFXID：2 (-1：SFXなし)</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>1412</SortID>
    </Field>
    <Field Def="s32 burnSfxId_3 = -1">
      <DisplayName>燃焼 SFXID：3</DisplayName>
      <Description>燃焼時のSFXID：3 (-1：SFXなし)</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>1413</SortID>
    </Field>
    <Field Def="s32 burnBulletBehaviorId = -1">
      <DisplayName>燃焼 弾発生 行動パラメータ：0</DisplayName>
      <Description>燃焼時の弾発生行動パラメータ：0(-1:発生しない)</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>1440</SortID>
    </Field>
    <Field Def="s32 burnBulletBehaviorId_1 = -1">
      <DisplayName>燃焼 弾発生 行動パラメータ：1</DisplayName>
      <Description>燃焼時の弾発生行動パラメータ：1(-1:発生しない)</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>1441</SortID>
    </Field>
    <Field Def="s32 burnBulletBehaviorId_2 = -1">
      <DisplayName>燃焼 弾発生 行動パラメータ：2</DisplayName>
      <Description>燃焼時の弾発生行動パラメータ：2(-1:発生しない)</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>1442</SortID>
    </Field>
    <Field Def="s32 burnBulletBehaviorId_3 = -1">
      <DisplayName>燃焼 弾発生 行動パラメータ：3</DisplayName>
      <Description>燃焼時の弾発生行動パラメータ：3(-1:発生しない)</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>1443</SortID>
    </Field>
    <Field Def="u16 burnBulletInterval = 30">
      <DisplayName>燃焼 弾発生間隔(フレーム)</DisplayName>
      <Description>延焼用の弾を発生する間隔(フレーム)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>9999</Maximum>
      <SortID>1450</SortID>
    </Field>
    <Field Def="u8 navimeshFlag">
      <DisplayName>ナビメッシュフラグ</DisplayName>
      <Enum>OBJECT_NAVIMESH_FLAG</Enum>
      <Description>オブジェから設定されるナビメッシュフラグ</Description>
      <EditFlags>None</EditFlags>
      <Maximum>99</Maximum>
      <SortID>1600</SortID>
    </Field>
    <Field Def="u8 collisionType">
      <DisplayName>衝突判定タイプ</DisplayName>
      <Enum>OBJECT_COLLISION_TYPE</Enum>
      <Description>衝突判定タイプ</Description>
      <Maximum>4</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="f32 burnBulletDelayTime">
      <DisplayName>燃焼 弾発生遅延時間(秒)</DisplayName>
      <Description>延焼用の弾発生を遅らせる時間(秒)</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1451</SortID>
    </Field>
    <Field Def="f32 burnSfxDelayTimeMin">
      <DisplayName>燃焼 SFX発生遅延 開始時間(秒)：0</DisplayName>
      <Description>燃焼時のSFX発生遅延時間 開始～終了時間の間でランダムに決まる</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1420</SortID>
    </Field>
    <Field Def="f32 burnSfxDelayTimeMin_1">
      <DisplayName>燃焼 SFX発生遅延 開始時間(秒)：1</DisplayName>
      <Description>燃焼時のSFX発生遅延時間 開始～終了時間の間でランダムに決まる</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1421</SortID>
    </Field>
    <Field Def="f32 burnSfxDelayTimeMin_2">
      <DisplayName>燃焼 SFX発生遅延 開始時間(秒)：2</DisplayName>
      <Description>燃焼時のSFX発生遅延時間 開始～終了時間の間でランダムに決まる</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1422</SortID>
    </Field>
    <Field Def="f32 burnSfxDelayTimeMin_3">
      <DisplayName>燃焼 SFX発生遅延 開始時間(秒)：3</DisplayName>
      <Description>燃焼時のSFX発生遅延時間 開始～終了時間の間でランダムに決まる</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1423</SortID>
    </Field>
    <Field Def="f32 burnSfxDelayTimeMax">
      <DisplayName>燃焼 SFX発生遅延 終了時間(秒)：0</DisplayName>
      <Description>燃焼時のSFX発生遅延時間 開始～終了時間の間でランダムに決まる</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1430</SortID>
    </Field>
    <Field Def="f32 burnSfxDelayTimeMax_1">
      <DisplayName>燃焼 SFX発生遅延 終了時間(秒)：1</DisplayName>
      <Description>燃焼時のSFX発生遅延時間 開始～終了時間の間でランダムに決まる</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1431</SortID>
    </Field>
    <Field Def="f32 burnSfxDelayTimeMax_2">
      <DisplayName>燃焼 SFX発生遅延 終了時間(秒)：2</DisplayName>
      <Description>燃焼時のSFX発生遅延時間 開始～終了時間の間でランダムに決まる</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1432</SortID>
    </Field>
    <Field Def="f32 burnSfxDelayTimeMax_3">
      <DisplayName>燃焼 SFX発生遅延 終了時間(秒)：3</DisplayName>
      <Description>燃焼時のSFX発生遅延時間 開始～終了時間の間でランダムに決まる</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1433</SortID>
    </Field>
    <Field Def="s32 BreakAiSoundID">
      <DisplayName>破壊時発生AI音ID</DisplayName>
      <Description>破壊時に発生させるAI音ID</Description>
      <Minimum>0</Minimum>
      <Maximum>*********</Maximum>
      <SortID>1500</SortID>
    </Field>
    <Field Def="f32 FragmentInvisibleWaitTime">
      <DisplayName>破片非表示 待機時間(秒)</DisplayName>
      <Description>破片のマテリアルID(-1：非表示処理を行なわない)</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1700</SortID>
    </Field>
    <Field Def="f32 FragmentInvisibleTime">
      <DisplayName>破片非表示 時間(秒)</DisplayName>
      <Description>破片を非表示にさせる時間(秒)</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1701</SortID>
    </Field>
    <Field Def="dummy8 pad_3[16] = -1">
      <DisplayName>パディング</DisplayName>
      <Description>破片のマテリアルID(-1：非表示処理を行なわない)</Description>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>127</Maximum>
      <Increment>1</Increment>
      <SortID>100000</SortID>
    </Field>
    <Field Def="f32 RigidPenetrationScale_Soft">
      <DisplayName>剛体 衝突点距離係数 [柔らかい]</DisplayName>
      <Description>剛体ソフトコンタクト設定 衝突点距離係数 [柔らかい]</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <Increment>0.001</Increment>
      <SortID>1800</SortID>
    </Field>
    <Field Def="f32 RigidPenetrationScale_Normal">
      <DisplayName>剛体 衝突点距離係数 [通常]</DisplayName>
      <Description>剛体ソフトコンタクト設定 衝突点距離係数 [通常]</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <Increment>0.001</Increment>
      <SortID>1801</SortID>
    </Field>
    <Field Def="f32 RigidPenetrationScale_Hard">
      <DisplayName>剛体 衝突点距離係数 [固い]</DisplayName>
      <Description>剛体ソフトコンタクト設定 衝突点距離係数 [固い]</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <Increment>0.001</Increment>
      <SortID>1802</SortID>
    </Field>
    <Field Def="s32 LandTouchSfxId = -1">
      <DisplayName>地形接触時のSFXID</DisplayName>
      <Description>地形接触時のSFXID(-1:地形のマテリアルによりオフセット)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>1E+09</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="u8 isDamageCover:1">
      <DisplayName>ダメージを遮蔽するか</DisplayName>
      <Description>ダメージを受けたときに、そのダメージを反対側に通さないかどうか　(0:通す, 1:通さない)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="dummy8 pad_4[1]">
      <DisplayName>パディング</DisplayName>
      <SortID>100001</SortID>
    </Field>
    <Field Def="u16 paintDecalTargetTextureSize = 256">
      <DisplayName>ペイントデカールターゲットサイズ</DisplayName>
      <Description>ペイントデカールターゲットサイズ(0～4096 ２のべき乗のみ許可)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>4096</Maximum>
      <Increment>128</Increment>
      <SortID>2300</SortID>
    </Field>
    <Field Def="f32 lifeTime_forDC">
      <DisplayName>動的生成Objの寿命(秒)</DisplayName>
      <Description>動的生成Objが生成後に消滅するまでの時間 (0:消滅しない)</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>2200</SortID>
    </Field>
    <Field Def="f32 clothUpdateDist">
      <DisplayName>クロス更新距離(m)</DisplayName>
      <Description>havokClothの更新を行なうカメラからの距離(0:必ず更新する)</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>2201</SortID>
    </Field>
    <Field Def="s32 contactSeId = -1">
      <DisplayName>プレイヤー接触時SE ID</DisplayName>
      <Description>自分が操作するローカルプレイヤーが触れた際に再生するSEのID(-1:再生しない)</Description>
      <Minimum>-1</Minimum>
      <Maximum>1E+09</Maximum>
      <SortID>990</SortID>
    </Field>
    <Field Def="s32 breakLandingSfxId = -1">
      <DisplayName>破壊後着地時SFX識別子</DisplayName>
      <Description>破壊された後、最初に着地した際に再生するオブジェ材質依存SFXの識別子(-1:発生しない)</Description>
      <Minimum>-1</Minimum>
      <Maximum>31</Maximum>
      <SortID>1102</SortID>
    </Field>
    <Field Def="s32 waypointDummyPolyId_0 = -1">
      <DisplayName>ウェイポイントダミポリID_0</DisplayName>
      <Description>ウェイポイントダミポリID_0(-1:なし)</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>2300</SortID>
    </Field>
    <Field Def="s32 waypointParamId_0 = -1">
      <DisplayName>ウェイポイントパラメータID_0</DisplayName>
      <Description>ウェイポイントパラメータID_0(-1:なし)</Description>
      <Minimum>-1</Minimum>
      <Maximum>*********</Maximum>
      <SortID>2302</SortID>
    </Field>
    <Field Def="s32 soundBankId = -1">
      <DisplayName>サウンドのバンクID</DisplayName>
      <Description>サウンドのバンクID(-1:バンクなし, それ以外:指定したIDのバンク)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2400</SortID>
    </Field>
    <Field Def="s32 refDrawParamId = -1">
      <DisplayName>描画パラメータ参照ID</DisplayName>
      <Description>描画パラメータの参照ID</Description>
      <Minimum>-1</Minimum>
      <Maximum>1E+09</Maximum>
      <SortID>2500</SortID>
    </Field>
    <Field Def="f32 autoCreateDynamicOffsetHeight = 0.1">
      <DisplayName>自動生成出現高さオフセット[m]</DisplayName>
      <Description>マップ自動生成OBJの出現高さオフセット[m]、レイキャストが当たったところから度ぐらい浮かすか</Description>
      <Minimum>0</Minimum>
      <Maximum>256</Maximum>
      <SortID>2601</SortID>
    </Field>
    <Field Def="s32 reserved0 = -1">
      <DisplayName>リザーブ</DisplayName>
      <Description>リザーブ</Description>
      <Minimum>-1</Minimum>
      <Maximum>1E+09</Maximum>
      <SortID>99999</SortID>
    </Field>
    <Field Def="s32 soundBreakSEId = -1">
      <DisplayName>破壊音SEID</DisplayName>
      <Description>破壊音SEID(9桁) -1：objIdから生成</Description>
      <Minimum>-1</Minimum>
      <Maximum>1E+09</Maximum>
      <SortID>2401</SortID>
    </Field>
    <Field Def="dummy8 pad_5[40]">
      <DisplayName>パディング</DisplayName>
      <SortID>99999</SortID>
    </Field>
  </Fields>
</PARAMDEF>