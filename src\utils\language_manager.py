"""
语言管理器
支持中英文切换功能
"""

import json
import os
from pathlib import Path
from typing import Dict, Any
from PySide6.QtCore import QObject, Signal


class LanguageManager(QObject):
    """语言管理器"""
    
    # 语言切换信号
    language_changed = Signal(str)  # 发送新语言代码
    
    def __init__(self):
        super().__init__()
        self.current_language = "zh_CN"  # 默认中文
        self.translations = {}
        self.config_file = Path("ESR/language_config.json")
        
        # 初始化
        self._load_config()
        self._load_translations()
    
    def _load_config(self):
        """加载语言配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.current_language = config.get("language", "zh_CN")
            else:
                # 创建默认配置
                self._save_config()
        except Exception as e:
            print(f"加载语言配置失败: {e}")
            self.current_language = "zh_CN"
    
    def _save_config(self):
        """保存语言配置"""
        try:
            # 确保目录存在
            self.config_file.parent.mkdir(exist_ok=True)
            
            config = {
                "language": self.current_language,
                "last_update": "2025-01-27"
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存语言配置失败: {e}")
    
    def _load_translations(self):
        """加载翻译文件"""
        try:
            # 中文翻译
            self.translations["zh_CN"] = self._get_chinese_translations()
            
            # 英文翻译
            self.translations["en_US"] = self._get_english_translations()
            
        except Exception as e:
            print(f"加载翻译失败: {e}")
    
    def _get_chinese_translations(self) -> Dict[str, str]:
        """获取中文翻译"""
        return {
            # 主窗口
            "app_title": "Nmodm - 艾尔登法环联机工具",
            "main_window_title": "艾尔登法环联机工具",
            
            # 侧边栏
            "sidebar_welcome": "首页",
            "sidebar_home": "快速启动",
            "sidebar_config": "基础配置",
            "sidebar_me3": "工具下载",
            "sidebar_mods": "Mod配置",
            "sidebar_bin_merge": "BIN合并",
            "sidebar_lan_gaming": "局域网配置",
            "sidebar_virtual_lan": "虚拟局域网",
            "sidebar_about": "关于",
            
            # 虚拟局域网页面
            "virtual_lan_title": "虚拟局域网",
            "room_name": "房间名称",
            "room_password": "房间密码",
            "player_name": "玩家名称",
            "start_network": "启动网络",
            "stop_network": "停止网络",
            "share_room": "分享房间",
            "join_room": "加入房间",
            "room_code": "房间代码",
            
            # 高级设置
            "advanced_settings": "高级设置",
            "enable_encryption": "启用加密",
            "enable_ipv6": "启用IPv6",
            "latency_first": "延迟优先",
            "multi_thread": "多线程",
            "enable_kcp_proxy": "启用KCP代理",
            "enable_quic_proxy": "启用QUIC代理",
            "enable_smoltcp": "启用用户态网络栈",
            "enable_compression": "启用压缩算法",
            
            # 网络优化
            "network_optimization": "网络优化",
            "winip_broadcast": "启用IP广播",
            "auto_metric": "自动网卡跃点",
            
            # 按钮和操作
            "save": "保存",
            "cancel": "取消",
            "confirm": "确认",
            "close": "关闭",
            "copy": "复制",
            "paste": "粘贴",
            "delete": "删除",
            "refresh": "刷新",
            
            # 状态信息
            "status_running": "运行中",
            "status_stopped": "已停止",
            "status_connecting": "连接中",
            "status_connected": "已连接",
            "status_disconnected": "已断开",
            
            # 消息提示
            "success": "成功",
            "error": "错误",
            "warning": "警告",
            "info": "信息",
            "loading": "加载中...",
            
            # 语言设置
            "language": "语言",
            "language_chinese": "中文",
            "language_english": "English",
            "language_switch_success": "语言切换成功",
            "language_switch_restart": "语言切换将在重启后生效",
        }
    
    def _get_english_translations(self) -> Dict[str, str]:
        """获取英文翻译"""
        return {
            # 主窗口
            "app_title": "Nmodm - Elden Ring Multiplayer Tool",
            "main_window_title": "Elden Ring Multiplayer Tool",
            
            # 侧边栏
            "sidebar_welcome": "Home",
            "sidebar_home": "Quick Start",
            "sidebar_config": "Basic Config",
            "sidebar_me3": "Tool Download",
            "sidebar_mods": "Mod Config",
            "sidebar_bin_merge": "BIN Merge",
            "sidebar_lan_gaming": "LAN Config",
            "sidebar_virtual_lan": "Virtual LAN",
            "sidebar_about": "About",
            
            # 虚拟局域网页面
            "virtual_lan_title": "Virtual LAN",
            "room_name": "Room Name",
            "room_password": "Room Password",
            "player_name": "Player Name",
            "start_network": "Start Network",
            "stop_network": "Stop Network",
            "share_room": "Share Room",
            "join_room": "Join Room",
            "room_code": "Room Code",
            
            # 高级设置
            "advanced_settings": "Advanced Settings",
            "enable_encryption": "Enable Encryption",
            "enable_ipv6": "Enable IPv6",
            "latency_first": "Latency First",
            "multi_thread": "Multi Thread",
            "enable_kcp_proxy": "Enable KCP Proxy",
            "enable_quic_proxy": "Enable QUIC Proxy",
            "enable_smoltcp": "Enable User-space Network Stack",
            "enable_compression": "Enable Compression",
            
            # 网络优化
            "network_optimization": "Network Optimization",
            "winip_broadcast": "Enable IP Broadcast",
            "auto_metric": "Auto Network Metric",
            
            # 按钮和操作
            "save": "Save",
            "cancel": "Cancel", 
            "confirm": "Confirm",
            "close": "Close",
            "copy": "Copy",
            "paste": "Paste",
            "delete": "Delete",
            "refresh": "Refresh",
            
            # 状态信息
            "status_running": "Running",
            "status_stopped": "Stopped",
            "status_connecting": "Connecting",
            "status_connected": "Connected",
            "status_disconnected": "Disconnected",
            
            # 消息提示
            "success": "Success",
            "error": "Error",
            "warning": "Warning",
            "info": "Info",
            "loading": "Loading...",
            
            # 语言设置
            "language": "Language",
            "language_chinese": "中文",
            "language_english": "English",
            "language_switch_success": "Language switched successfully",
            "language_switch_restart": "Language change will take effect after restart",
        }
    
    def get_text(self, key: str, default: str = None) -> str:
        """获取翻译文本"""
        try:
            translations = self.translations.get(self.current_language, {})
            return translations.get(key, default or key)
        except Exception:
            return default or key
    
    def set_language(self, language_code: str) -> bool:
        """设置语言"""
        try:
            if language_code in self.translations:
                old_language = self.current_language
                self.current_language = language_code
                self._save_config()
                
                # 发送语言切换信号
                self.language_changed.emit(language_code)
                
                print(f"语言已切换: {old_language} -> {language_code}")
                return True
            else:
                print(f"不支持的语言: {language_code}")
                return False
                
        except Exception as e:
            print(f"设置语言失败: {e}")
            return False
    
    def get_current_language(self) -> str:
        """获取当前语言"""
        return self.current_language
    
    def get_available_languages(self) -> Dict[str, str]:
        """获取可用语言列表"""
        return {
            "zh_CN": self.get_text("language_chinese"),
            "en_US": self.get_text("language_english")
        }


# 全局语言管理器实例
language_manager = LanguageManager()


def tr(key: str, default: str = None) -> str:
    """翻译函数的简化版本"""
    return language_manager.get_text(key, default)
