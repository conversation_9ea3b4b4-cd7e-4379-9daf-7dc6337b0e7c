﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>RESIST_CORRECT_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 addPoint1">
      <DisplayName>1回目発動後加算pt</DisplayName>
      <Description>状態異常が1回発動した後に耐性値に加算される値</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-999.99</Minimum>
      <Maximum>999.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>1</SortID>
    </Field>
    <Field Def="f32 addPoint2">
      <DisplayName>2回目発動後加算pt</DisplayName>
      <Description>状態異常が2回発動した後に耐性値に加算される値</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-999.99</Minimum>
      <Maximum>999.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>2</SortID>
    </Field>
    <Field Def="f32 addPoint3">
      <DisplayName>3回目発動後加算pt</DisplayName>
      <Description>状態異常が3回発動した後に耐性値に加算される値</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-999.99</Minimum>
      <Maximum>999.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>3</SortID>
    </Field>
    <Field Def="f32 addPoint4">
      <DisplayName>4回目発動後加算pt</DisplayName>
      <Description>状態異常が4回発動した後に耐性値に加算される値</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-999.99</Minimum>
      <Maximum>999.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>4</SortID>
    </Field>
    <Field Def="f32 addPoint5">
      <DisplayName>5回目発動後加算pt</DisplayName>
      <Description>状態異常が5回発動した後に耐性値に加算される値</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-999.99</Minimum>
      <Maximum>999.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>5</SortID>
    </Field>
    <Field Def="f32 addRate1 = 1">
      <DisplayName>1回目発動後倍率</DisplayName>
      <Description>状態異常が1回発動した後に耐性値に掛かる倍率</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-999.99</Minimum>
      <Maximum>999.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>6</SortID>
    </Field>
    <Field Def="f32 addRate2 = 1">
      <DisplayName>2回目発動後倍率</DisplayName>
      <Description>状態異常が2回発動した後に耐性値に掛かる倍率</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-999.99</Minimum>
      <Maximum>999.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>7</SortID>
    </Field>
    <Field Def="f32 addRate3 = 1">
      <DisplayName>3回目発動後倍率</DisplayName>
      <Description>状態異常が3回発動した後に耐性値に掛かる倍率</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-999.99</Minimum>
      <Maximum>999.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>8</SortID>
    </Field>
    <Field Def="f32 addRate4 = 1">
      <DisplayName>4回目発動後倍率</DisplayName>
      <Description>状態異常が4回発動した後に耐性値に掛かる倍率</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-999.99</Minimum>
      <Maximum>999.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>9</SortID>
    </Field>
    <Field Def="f32 addRate5 = 1">
      <DisplayName>5回目発動後倍率</DisplayName>
      <Description>状態異常が5回発動した後に耐性値に掛かる倍率</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-999.99</Minimum>
      <Maximum>999.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>10</SortID>
    </Field>
  </Fields>
</PARAMDEF>