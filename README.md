# Nmodm - 现代化游戏管理工具

<div align="center">

![Nmodm Logo](zwnr.png)

**专为《艾尔登法环：夜之君临》设计的现代化游戏管理工具**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![PySide6](https://img.shields.io/badge/PySide6-6.0+-green.svg)](https://pyside.org)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Windows-lightgrey.svg)](https://windows.microsoft.com)

</div>

## ✨ 特性

- 🎮 **游戏管理**: 一键配置游戏路径和启动参数
- 🔧 **ME3 工具集成**: 自动下载和管理 ME3 工具
- 🎯 **智能 Mod 管理**: 支持内部和外部 Mod，智能类型检测
- 🌐 **局域网联机**: 完整的局域网联机配置和启动功能
- 🔗 **BIN 文件合并**: 集成 ERModsMerger 进行 regulation.bin 合并
- 💬 **Mod 注释系统**: 为 Mod 添加自定义注释和说明
- 🚀 **快速启动**: 一键启动配置好的游戏
- 🎨 **现代化界面**: 基于 PySide6 的现代无边框设计
- 📦 **便携部署**: 支持单文件和目录两种打包模式

## 🖼️ 界面预览

### 主界面
- 现代化无边框设计
- 侧边栏导航
- 实时状态显示

### 核心功能
- **快速启动**: 显示配置概览，一键启动游戏
- **游戏配置**: 设置游戏路径和在线修复
- **ME3 工具**: 下载和管理 ME3 工具
- **Mod 配置**: 智能 Mod 管理和配置生成
- **BIN 合并**: regulation.bin 文件合并功能
- **局域网联机**: 局域网多人游戏配置和启动

## 🚀 快速开始

### 方式一：直接使用（推荐）

1. 从 [Releases](../../releases) 下载最新版本
2. 解压到任意目录
3. 运行 `Nmodm.exe`

### 方式二：源码运行

```bash
# 克隆项目
git clone <repository-url>
cd Nmodm

# 安装依赖
pip install -r requirements.txt

# 运行应用
python main.py
```

## 📖 使用指南

### 基础使用流程

1. **配置游戏路径**: 在"基础配置"页面设置 `nightreign.exe` 路径
2. **下载 ME3 工具**: 在"工具下载"页面下载最新版本
3. **管理 Mod**: 在"Mod配置"页面添加和配置 Mod
4. **合并 BIN 文件**: 在"BIN合并"页面合并 regulation.bin 文件
5. **配置局域网联机**: 在"局域网联机"页面设置联机参数
6. **启动游戏**: 在"快速启动"页面一键启动

### 高级功能

- **外部 Mod 支持**: 添加任意位置的 Mod 文件
- **智能类型检测**: 自动识别文件夹型和 DLL 型 Mod
- **配置文件生成**: 实时生成 ME3 兼容的配置文件
- **镜像切换**: 根据网络情况选择最佳下载镜像

## 📚 文档

- [📖 软件使用指南](软件使用指南.md) - 详细的用户使用说明
- [🔧 项目使用指南](项目使用指南.md) - 开发者和贡献者指南
- [📦 打包使用说明](打包使用说明.md) - 应用打包和部署指南

## 🛠️ 技术栈

- **界面框架**: PySide6 (Qt6)
- **编程语言**: Python 3.8+
- **配置格式**: JSON, TOML
- **打包工具**: PyInstaller, Nuitka
- **网络请求**: requests
- **文件处理**: pathlib, zipfile

## 🏗️ 项目结构

```
Nmodm/
├── main.py                    # 应用入口
├── src/                       # 源代码
│   ├── app.py                # 应用主类
│   ├── config/               # 配置管理
│   ├── ui/                   # 用户界面
│   └── utils/                # 工具类
├── OnlineFix/                # 在线修复文件
├── Mods/                     # Mod 文件夹
├── Builds/                   # 打包输出
├── build_*.py                # 打包脚本
└── docs/                     # 文档
```

## 🔧 开发

### 环境准备

```bash
# 创建虚拟环境
python -m venv .venv
.venv\Scripts\activate

# 安装开发依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### 运行开发版本

```bash
python main.py
```

### 打包应用

```bash
# 使用统一管理器
python build_manager.py

# 或单独使用
python build_pyinstaller.py  # PyInstaller
python build_nuitka.py       # Nuitka
```

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [ME3 项目](https://me3.readthedocs.io/) - 提供强大的 Mod 管理工具
- [PySide6](https://pyside.org/) - 优秀的 Python GUI 框架
- [PyDracula](https://github.com/Wanderson-Magalhaes/Modern_GUI_PyDracula_PySide6_or_PyQt6) - 现代化界面设计灵感

## 📞 支持

如有问题或建议：

- 📖 查看 [使用指南](软件使用指南.md)
- 🐛 提交 [Issue](../../issues)
- 💬 参与 [讨论](../../discussions)

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给个 Star！**

Made with ❤️ for Elden Ring: Nightreign Community

</div>
