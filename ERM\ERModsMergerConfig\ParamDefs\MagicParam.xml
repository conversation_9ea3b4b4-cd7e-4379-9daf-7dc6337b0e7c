﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>MAGIC_PARAM_ST</ParamType>
  <DataVersion>6</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 disableParam_NT:1">
      <DisplayName>NT版出力から外すか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータをNT版パッケージでは除外します</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
    </Field>
    <Field Def="dummy8 disableParamReserve1:7">
      <DisplayName>パッケージ出力用リザーブ1</DisplayName>
      <Description>パッケージ出力用リザーブ1</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>10151</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve2[3]">
      <DisplayName>パッケージ出力用リザーブ2</DisplayName>
      <Description>パッケージ出力用リザーブ2</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>10152</SortID>
    </Field>
    <Field Def="s32 yesNoDialogMessageId">
      <DisplayName>Yes/NoダイアログメッセージID</DisplayName>
      <Description>魔法使用時に出すYes/NoダイアログのメッセージID</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>2500</SortID>
    </Field>
    <Field Def="s32 limitCancelSpEffectId = -1">
      <DisplayName>使用制限から外れる特殊効果ID</DisplayName>
      <Description>指定した特殊効果IDが発動している時は使用制限を無視できる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1020</SortID>
    </Field>
    <Field Def="s16 sortId">
      <DisplayName>SortID</DisplayName>
      <Description>ソートID(-1:集めない)</Description>
      <Minimum>-1</Minimum>
      <Maximum>30000</Maximum>
      <SortID>1800</SortID>
    </Field>
    <Field Def="u8 requirementLuck">
      <DisplayName>装備条件【運】</DisplayName>
      <Description>PCの運がこれ以上無いと装備できない</Description>
      <Maximum>99</Maximum>
      <SortID>1603</SortID>
    </Field>
    <Field Def="u8 aiNotifyType">
      <DisplayName>AI通知タイプ</DisplayName>
      <Enum>MAGIC_AI_NOTIFY_TYPE</Enum>
      <Description>act("魔法発動時AI通知")でAIインタラプトが発生する</Description>
      <Maximum>2</Maximum>
      <SortID>9150</SortID>
    </Field>
    <Field Def="s16 mp">
      <DisplayName>消費MP[通常]</DisplayName>
      <Description>消費MP</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="s16 stamina">
      <DisplayName>消費スタミナ[通常]</DisplayName>
      <Description>消費スタミナ</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>310</SortID>
    </Field>
    <Field Def="s16 iconId">
      <DisplayName>アイコンID</DisplayName>
      <Description>アイコンを指定　＞メニュー用</Description>
      <Minimum>-1</Minimum>
      <Maximum>30000</Maximum>
      <SortID>160</SortID>
    </Field>
    <Field Def="s16 behaviorId">
      <DisplayName>行動ID</DisplayName>
      <Description>行動IDを設定する</Description>
      <Minimum>-1</Minimum>
      <Maximum>30000</Maximum>
      <SortID>1400</SortID>
    </Field>
    <Field Def="s16 mtrlItemId = -1">
      <DisplayName>必要アイテムID</DisplayName>
      <Description>購入に必要なアイテムID</Description>
      <Minimum>-1</Minimum>
      <Maximum>30000</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="s16 replaceMagicId = -1">
      <DisplayName>差し替える魔法ID</DisplayName>
      <Description>状態変化一致時に差し替えるID(-1:無効)</Description>
      <Minimum>-1</Minimum>
      <Maximum>30000</Maximum>
      <SortID>3000</SortID>
    </Field>
    <Field Def="s16 maxQuantity">
      <DisplayName>最大個数</DisplayName>
      <Description>１個当たりの個数(-1:無限)</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>450</SortID>
    </Field>
    <Field Def="u8 refCategory1">
      <DisplayName>IDカテゴリ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>呼び出しIDのカテゴリ[攻撃、飛び道具、特殊効果]</Description>
      <SortID>500</SortID>
      <UnkC8>スロット1</UnkC8>
    </Field>
    <Field Def="u8 overDexterity">
      <DisplayName>技量オーバー開始値</DisplayName>
      <Description>技量オーバー開始値</Description>
      <Maximum>99</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="u8 refCategory2">
      <DisplayName>IDカテゴリ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>呼び出しIDのカテゴリ[攻撃、飛び道具、特殊効果]</Description>
      <SortID>510</SortID>
      <UnkC8>スロット2</UnkC8>
    </Field>
    <Field Def="u8 slotLength">
      <DisplayName>必要スロット</DisplayName>
      <Description>装備に必要なスロット数 ＞メニュー用</Description>
      <Maximum>3</Maximum>
      <SortID>1600</SortID>
    </Field>
    <Field Def="u8 requirementIntellect">
      <DisplayName>装備条件【知力】</DisplayName>
      <Description>PCの知力がこれ以上無いと装備できない</Description>
      <Maximum>99</Maximum>
      <SortID>1601</SortID>
    </Field>
    <Field Def="u8 requirementFaith">
      <DisplayName>装備条件【理力】</DisplayName>
      <Description>PCの理力がこれ以上無いと装備できない</Description>
      <Maximum>99</Maximum>
      <SortID>1602</SortID>
    </Field>
    <Field Def="u8 analogDexterityMin">
      <DisplayName>アナログ最低技量</DisplayName>
      <Description>モーションキャンセルアナログ化：最低技量値</Description>
      <Maximum>99</Maximum>
      <SortID>1005</SortID>
    </Field>
    <Field Def="u8 analogDexterityMax">
      <DisplayName>アナログ最大技量</DisplayName>
      <Description>モーションキャンセルアナログ化：最高技量値</Description>
      <Maximum>99</Maximum>
      <SortID>1006</SortID>
    </Field>
    <Field Def="u8 ezStateBehaviorType">
      <DisplayName>カテゴリ</DisplayName>
      <Enum>MAGIC_CATEGORY</Enum>
      <Description>並べ替えに使用　＞メニュー用</Description>
      <SortID>100</SortID>
    </Field>
    <Field Def="u8 refCategory3">
      <DisplayName>IDカテゴリ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>呼び出しIDのカテゴリ[攻撃、飛び道具、特殊効果]</Description>
      <SortID>520</SortID>
      <UnkC8>スロット3</UnkC8>
    </Field>
    <Field Def="u8 spEffectCategory">
      <DisplayName>特殊効果カテゴリ</DisplayName>
      <Enum>BEHAVIOR_CATEGORY</Enum>
      <Description>スキルや、魔法、アイテムなどで、パラメータが変動する効果（エンチャントウェポンなど）があるので、│定した効果が、「武器攻撃のみをパワーアップする」といった効果に対応できるように行動ごとに設定するバリスタなど、設定の必要のないものは「なし」を設定する
</Description>
      <SortID>900</SortID>
    </Field>
    <Field Def="u8 refType">
      <DisplayName>モーションカテゴリ</DisplayName>
      <Enum>MAGIC_MOTION_TYPE</Enum>
      <Description>モーションを指定　＞EzState用</Description>
      <SortID>1000</SortID>
    </Field>
    <Field Def="u8 opmeMenuType">
      <DisplayName>使用時メニュータイプ</DisplayName>
      <Enum>GOODS_OPEN_MENU</Enum>
      <Description>魔法使用時に出すメニュータイプ</Description>
      <SortID>2600</SortID>
    </Field>
    <Field Def="u8 refCategory4">
      <DisplayName>IDカテゴリ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>呼び出しIDのカテゴリ[攻撃、飛び道具、特殊効果]</Description>
      <SortID>530</SortID>
      <UnkC8>スロット4</UnkC8>
    </Field>
    <Field Def="u16 hasSpEffectType">
      <DisplayName>どの常態か？</DisplayName>
      <Enum>SP_EFFECT_TYPE</Enum>
      <Description>魔法IDを差し替える必要がある状態変化を指定</Description>
      <Maximum>255</Maximum>
      <SortID>2800</SortID>
    </Field>
    <Field Def="u8 replaceCategory">
      <DisplayName>差し替えカテゴリ</DisplayName>
      <Enum>REPLACE_CATEGORY</Enum>
      <Description>魔法IDを差し替える時の追加条件</Description>
      <SortID>3100</SortID>
    </Field>
    <Field Def="u8 useLimitCategory">
      <DisplayName>特殊効果カテゴリによる使用制限</DisplayName>
      <Enum>SP_EFFECT_USELIMIT_CATEGORY</Enum>
      <Description>特殊効果によって使用可能かどうかを制御する為に指定</Description>
      <SortID>1010</SortID>
    </Field>
    <Field Def="u8 vowType0:1">
      <DisplayName>誓約0</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約0</Description>
      <Maximum>1</Maximum>
      <SortID>10000</SortID>
    </Field>
    <Field Def="u8 vowType1:1">
      <DisplayName>誓約1</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約1</Description>
      <Maximum>1</Maximum>
      <SortID>10010</SortID>
    </Field>
    <Field Def="u8 vowType2:1">
      <DisplayName>誓約2</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約2</Description>
      <Maximum>1</Maximum>
      <SortID>10020</SortID>
    </Field>
    <Field Def="u8 vowType3:1">
      <DisplayName>誓約3</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約3</Description>
      <Maximum>1</Maximum>
      <SortID>10030</SortID>
    </Field>
    <Field Def="u8 vowType4:1">
      <DisplayName>誓約4</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約4</Description>
      <Maximum>1</Maximum>
      <SortID>10040</SortID>
    </Field>
    <Field Def="u8 vowType5:1">
      <DisplayName>誓約5</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約5</Description>
      <Maximum>1</Maximum>
      <SortID>10050</SortID>
    </Field>
    <Field Def="u8 vowType6:1">
      <DisplayName>誓約6</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約6</Description>
      <Maximum>1</Maximum>
      <SortID>10060</SortID>
    </Field>
    <Field Def="u8 vowType7:1">
      <DisplayName>誓約7</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約7</Description>
      <Maximum>1</Maximum>
      <SortID>10070</SortID>
    </Field>
    <Field Def="u8 enable_multi:1">
      <DisplayName>マルチでも使用可能か</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>マルチでも使用できるか。シングル、マルチ両方で使える</Description>
      <Maximum>1</Maximum>
      <SortID>1100</SortID>
    </Field>
    <Field Def="u8 enable_multi_only:1">
      <DisplayName>マルチ専用か</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>マルチ専用か。シングルのときには使えない。マルチのときは使える。</Description>
      <Maximum>1</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="u8 isEnchant:1">
      <DisplayName>エンチャントか</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>エンチャントする魔法か</Description>
      <Maximum>1</Maximum>
      <SortID>1700</SortID>
    </Field>
    <Field Def="u8 isShieldEnchant:1">
      <DisplayName>盾エンチャントか</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>ガード・盾エンチャントする魔法か</Description>
      <Maximum>1</Maximum>
      <SortID>1710</SortID>
    </Field>
    <Field Def="u8 enable_live:1">
      <DisplayName>生存使用可</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>生存キャラが使用可能か</Description>
      <Maximum>1</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="u8 enable_gray:1">
      <DisplayName>グレイ使用可</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>グレイキャラが使用可能か</Description>
      <Maximum>1</Maximum>
      <SortID>2200</SortID>
    </Field>
    <Field Def="u8 enable_white:1">
      <DisplayName>白使用可</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>白ゴーストキャラが使用可能か</Description>
      <Maximum>1</Maximum>
      <SortID>2300</SortID>
    </Field>
    <Field Def="u8 enable_black:1">
      <DisplayName>黒使用可</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>黒ゴーストキャラが使用可能か</Description>
      <Maximum>1</Maximum>
      <SortID>2400</SortID>
    </Field>
    <Field Def="u8 disableOffline:1">
      <DisplayName>オフラインで使用不可か</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>オフラインで使用不可か</Description>
      <Maximum>1</Maximum>
      <SortID>2700</SortID>
    </Field>
    <Field Def="u8 castResonanceMagic:1">
      <DisplayName>共鳴魔法配信するか</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>共鳴魔法配信するか</Description>
      <Maximum>1</Maximum>
      <SortID>3200</SortID>
    </Field>
    <Field Def="u8 isValidTough_ProtSADmg:1">
      <DisplayName>防具SAダメージ倍率が初期値でも有効か？</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>防具SAが初期値でも強靭度計算が行われるかどうか。詳細は強靭度仕様書.xlsxを確認してください</Description>
      <Maximum>1</Maximum>
      <SortID>740</SortID>
    </Field>
    <Field Def="u8 isWarpMagic:1">
      <DisplayName>ワープ魔法か</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>ワープする魔法か。ここにチェックが入っている魔法は特殊効果「ワープ禁止」により使用が禁止されます</Description>
      <Maximum>1</Maximum>
      <SortID>3300</SortID>
    </Field>
    <Field Def="u8 enableRiding:1">
      <DisplayName>騎乗中使用可能か</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>騎乗中使用可能か</Description>
      <Maximum>1</Maximum>
      <SortID>2710</SortID>
    </Field>
    <Field Def="u8 disableRiding:1">
      <DisplayName>非騎乗中使用禁止か</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>非騎乗中使用禁止か</Description>
      <Maximum>1</Maximum>
      <SortID>2720</SortID>
    </Field>
    <Field Def="u8 isUseNoAttackRegion:1">
      <DisplayName>攻撃禁止区域で使用できるか</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>攻撃禁止区域で使用できるか</Description>
      <Maximum>1</Maximum>
      <SortID>2730</SortID>
    </Field>
    <Field Def="dummy8 pad_1:1">
      <DisplayName>pading</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>10153</SortID>
    </Field>
    <Field Def="u8 vowType8:1">
      <DisplayName>誓約8</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約8</Description>
      <Maximum>1</Maximum>
      <SortID>10080</SortID>
    </Field>
    <Field Def="u8 vowType9:1">
      <DisplayName>誓約9</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約9</Description>
      <Maximum>1</Maximum>
      <SortID>10090</SortID>
    </Field>
    <Field Def="u8 vowType10:1">
      <DisplayName>誓約10</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約10</Description>
      <Maximum>1</Maximum>
      <SortID>10100</SortID>
    </Field>
    <Field Def="u8 vowType11:1">
      <DisplayName>誓約11</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約11</Description>
      <Maximum>1</Maximum>
      <SortID>10110</SortID>
    </Field>
    <Field Def="u8 vowType12:1">
      <DisplayName>誓約12</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約12</Description>
      <Maximum>1</Maximum>
      <SortID>10120</SortID>
    </Field>
    <Field Def="u8 vowType13:1">
      <DisplayName>誓約13</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約13</Description>
      <Maximum>1</Maximum>
      <SortID>10130</SortID>
    </Field>
    <Field Def="u8 vowType14:1">
      <DisplayName>誓約14</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約14</Description>
      <Maximum>1</Maximum>
      <SortID>10140</SortID>
    </Field>
    <Field Def="u8 vowType15:1">
      <DisplayName>誓約15</DisplayName>
      <Enum>MAGIC_BOOL</Enum>
      <Description>誓約15</Description>
      <Maximum>1</Maximum>
      <SortID>10150</SortID>
    </Field>
    <Field Def="s32 castSfxId = -1">
      <DisplayName>詠唱SFXID</DisplayName>
      <Description>魔法詠唱中のSFXID</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="s32 fireSfxId = -1">
      <DisplayName>発動SFXID</DisplayName>
      <Description>魔法発動時のSFXID</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>801</SortID>
    </Field>
    <Field Def="s32 effectSfxId = -1">
      <DisplayName>効果SFXID</DisplayName>
      <Description>魔法効果中のSFXID</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>802</SortID>
    </Field>
    <Field Def="f32 toughnessCorrectRate">
      <DisplayName>強靭度 補正倍率</DisplayName>
      <Description>強靭度の基本値を補正する倍率です</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <SortID>750</SortID>
    </Field>
    <Field Def="u8 ReplacementStatusType">
      <DisplayName>差し替えステータスタイプ</DisplayName>
      <Enum>MAGIC_STATUS_TYPE</Enum>
      <Description>差し替えステータスタイプ</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>100</Maximum>
      <SortID>7000</SortID>
    </Field>
    <Field Def="s8 ReplacementStatus1 = -1">
      <DisplayName>差し替えステータス値1</DisplayName>
      <Description>差し替えステータス値1</Description>
      <Minimum>-1</Minimum>
      <Maximum>100</Maximum>
      <SortID>7010</SortID>
    </Field>
    <Field Def="s8 ReplacementStatus2 = -1">
      <DisplayName>差し替えステータス値2</DisplayName>
      <Description>差し替えステータス値2</Description>
      <Minimum>-1</Minimum>
      <Maximum>100</Maximum>
      <SortID>7020</SortID>
    </Field>
    <Field Def="s8 ReplacementStatus3 = -1">
      <DisplayName>差し替えステータス値3</DisplayName>
      <Description>差し替えステータス値3</Description>
      <Minimum>-1</Minimum>
      <Maximum>100</Maximum>
      <SortID>7030</SortID>
    </Field>
    <Field Def="s8 ReplacementStatus4 = -1">
      <DisplayName>差し替えステータス値4</DisplayName>
      <Description>差し替えステータス値4</Description>
      <Minimum>-1</Minimum>
      <Maximum>100</Maximum>
      <SortID>7040</SortID>
    </Field>
    <Field Def="u8 refCategory5">
      <DisplayName>IDカテゴリ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>呼び出しIDのカテゴリ[攻撃、飛び道具、特殊効果]</Description>
      <SortID>540</SortID>
      <UnkC8>スロット5</UnkC8>
    </Field>
    <Field Def="s16 consumeSA">
      <DisplayName>消費SA[通常/溜め]</DisplayName>
      <Description>消費SA量[通常/溜め]</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="s32 ReplacementMagic1 = -1">
      <DisplayName>差し替えID1</DisplayName>
      <Description>差し替えID1</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>7200</SortID>
    </Field>
    <Field Def="s32 ReplacementMagic2 = -1">
      <DisplayName>差し替えID2</DisplayName>
      <Description>差し替えID2</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>7210</SortID>
    </Field>
    <Field Def="s32 ReplacementMagic3 = -1">
      <DisplayName>差し替えID3</DisplayName>
      <Description>差し替えID3</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>7220</SortID>
    </Field>
    <Field Def="s32 ReplacementMagic4 = -1">
      <DisplayName>差し替えID4</DisplayName>
      <Description>差し替えID4</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>7230</SortID>
    </Field>
    <Field Def="s16 mp_charge">
      <DisplayName>消費MP[溜め]</DisplayName>
      <Description>消費MP[溜め]</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>350</SortID>
    </Field>
    <Field Def="s16 stamina_charge">
      <DisplayName>消費スタミナ[溜め]</DisplayName>
      <Description>消費スタミナ[溜め]</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>360</SortID>
    </Field>
    <Field Def="u8 createLimitGroupId">
      <DisplayName>作成制限グループId</DisplayName>
      <Description>0なら未使用。指定したグループIdの弾丸作成数を確認し、上限に達していたら魔法の使用をできなくする。</Description>
      <SortID>9000</SortID>
    </Field>
    <Field Def="u8 refCategory6">
      <DisplayName>IDカテゴリ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>呼び出しIDのカテゴリ[攻撃、飛び道具、特殊効果]</Description>
      <SortID>550</SortID>
      <UnkC8>スロット6</UnkC8>
    </Field>
    <Field Def="u8 subCategory1">
      <DisplayName>サブカテゴリ1</DisplayName>
      <Enum>ATK_SUB_CATEGORY</Enum>
      <Description>サブカテゴリ1</Description>
      <SortID>950</SortID>
    </Field>
    <Field Def="u8 subCategory2">
      <DisplayName>サブカテゴリ2</DisplayName>
      <Enum>ATK_SUB_CATEGORY</Enum>
      <Description>サブカテゴリ2</Description>
      <SortID>960</SortID>
    </Field>
    <Field Def="u8 refCategory7">
      <DisplayName>IDカテゴリ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>呼び出しIDのカテゴリ[攻撃、飛び道具、特殊効果]</Description>
      <SortID>560</SortID>
      <UnkC8>スロット7</UnkC8>
    </Field>
    <Field Def="u8 refCategory8">
      <DisplayName>IDカテゴリ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>呼び出しIDのカテゴリ[攻撃、飛び道具、特殊効果]</Description>
      <SortID>570</SortID>
      <UnkC8>スロット8</UnkC8>
    </Field>
    <Field Def="u8 refCategory9">
      <DisplayName>IDカテゴリ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>呼び出しIDのカテゴリ[攻撃、飛び道具、特殊効果]</Description>
      <SortID>580</SortID>
      <UnkC8>スロット9</UnkC8>
    </Field>
    <Field Def="u8 refCategory10">
      <DisplayName>IDカテゴリ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>呼び出しIDのカテゴリ[攻撃、飛び道具、特殊効果]</Description>
      <SortID>590</SortID>
      <UnkC8>スロット10</UnkC8>
    </Field>
    <Field Def="s32 refId1 = -1">
      <DisplayName>呼び出しID</DisplayName>
      <Description>魔法から呼び出すID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>501</SortID>
      <UnkC8>スロット1</UnkC8>
    </Field>
    <Field Def="s32 refId2 = -1">
      <DisplayName>呼び出しID</DisplayName>
      <Description>魔法から呼び出すID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>511</SortID>
      <UnkC8>スロット2</UnkC8>
    </Field>
    <Field Def="s32 refId3 = -1">
      <DisplayName>呼び出しID</DisplayName>
      <Description>魔法から呼び出すID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>521</SortID>
      <UnkC8>スロット3</UnkC8>
    </Field>
    <Field Def="s32 aiUseJudgeId = -1">
      <DisplayName>AI使用判断ID</DisplayName>
      <Description>AI使用判断ID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>9100</SortID>
    </Field>
    <Field Def="s32 refId4 = -1">
      <DisplayName>呼び出しID</DisplayName>
      <Description>魔法から呼び出すID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>531</SortID>
      <UnkC8>スロット4</UnkC8>
    </Field>
    <Field Def="s32 refId5 = -1">
      <DisplayName>呼び出しID</DisplayName>
      <Description>魔法から呼び出すID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>541</SortID>
      <UnkC8>スロット5</UnkC8>
    </Field>
    <Field Def="s32 refId6 = -1">
      <DisplayName>呼び出しID</DisplayName>
      <Description>魔法から呼び出すID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>551</SortID>
      <UnkC8>スロット6</UnkC8>
    </Field>
    <Field Def="s32 refId7 = -1">
      <DisplayName>呼び出しID</DisplayName>
      <Description>魔法から呼び出すID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>561</SortID>
      <UnkC8>スロット7</UnkC8>
    </Field>
    <Field Def="s32 refId8 = -1">
      <DisplayName>呼び出しID</DisplayName>
      <Description>魔法から呼び出すID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>571</SortID>
      <UnkC8>スロット8</UnkC8>
    </Field>
    <Field Def="s32 refId9 = -1">
      <DisplayName>呼び出しID</DisplayName>
      <Description>魔法から呼び出すID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>581</SortID>
      <UnkC8>スロット9</UnkC8>
    </Field>
    <Field Def="s32 refId10 = -1">
      <DisplayName>呼び出しID</DisplayName>
      <Description>魔法から呼び出すID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>591</SortID>
      <UnkC8>スロット10</UnkC8>
    </Field>
    <Field Def="u8 consumeType1">
      <DisplayName>消費タイプ</DisplayName>
      <Enum>MAGIC_CONSUME_TYPE</Enum>
      <Description>消費タイプ</Description>
      <Maximum>2</Maximum>
      <SortID>502</SortID>
      <UnkC8>スロット1</UnkC8>
    </Field>
    <Field Def="u8 consumeType2">
      <DisplayName>消費タイプ</DisplayName>
      <Enum>MAGIC_CONSUME_TYPE</Enum>
      <Description>消費タイプ</Description>
      <Maximum>2</Maximum>
      <SortID>512</SortID>
      <UnkC8>スロット2</UnkC8>
    </Field>
    <Field Def="u8 consumeType3">
      <DisplayName>消費タイプ</DisplayName>
      <Enum>MAGIC_CONSUME_TYPE</Enum>
      <Description>消費タイプ</Description>
      <Maximum>2</Maximum>
      <SortID>522</SortID>
      <UnkC8>スロット3</UnkC8>
    </Field>
    <Field Def="u8 consumeType4">
      <DisplayName>消費タイプ</DisplayName>
      <Enum>MAGIC_CONSUME_TYPE</Enum>
      <Description>消費タイプ</Description>
      <Maximum>2</Maximum>
      <SortID>532</SortID>
      <UnkC8>スロット4</UnkC8>
    </Field>
    <Field Def="u8 consumeType5">
      <DisplayName>消費タイプ</DisplayName>
      <Enum>MAGIC_CONSUME_TYPE</Enum>
      <Description>消費タイプ</Description>
      <Maximum>2</Maximum>
      <SortID>542</SortID>
      <UnkC8>スロット5</UnkC8>
    </Field>
    <Field Def="u8 consumeType6">
      <DisplayName>消費タイプ</DisplayName>
      <Enum>MAGIC_CONSUME_TYPE</Enum>
      <Description>消費タイプ</Description>
      <Maximum>2</Maximum>
      <SortID>552</SortID>
      <UnkC8>スロット6</UnkC8>
    </Field>
    <Field Def="u8 consumeType7">
      <DisplayName>消費タイプ</DisplayName>
      <Enum>MAGIC_CONSUME_TYPE</Enum>
      <Description>消費タイプ</Description>
      <Maximum>2</Maximum>
      <SortID>562</SortID>
      <UnkC8>スロット7</UnkC8>
    </Field>
    <Field Def="u8 consumeType8">
      <DisplayName>消費タイプ</DisplayName>
      <Enum>MAGIC_CONSUME_TYPE</Enum>
      <Description>消費タイプ</Description>
      <Maximum>2</Maximum>
      <SortID>572</SortID>
      <UnkC8>スロット8</UnkC8>
    </Field>
    <Field Def="u8 consumeType9">
      <DisplayName>消費タイプ</DisplayName>
      <Enum>MAGIC_CONSUME_TYPE</Enum>
      <Description>消費タイプ</Description>
      <Maximum>2</Maximum>
      <SortID>582</SortID>
      <UnkC8>スロット9</UnkC8>
    </Field>
    <Field Def="u8 consumeType10">
      <DisplayName>消費タイプ</DisplayName>
      <Enum>MAGIC_CONSUME_TYPE</Enum>
      <Description>消費タイプ</Description>
      <Maximum>2</Maximum>
      <SortID>592</SortID>
      <UnkC8>スロット10</UnkC8>
    </Field>
    <Field Def="s16 consumeLoopMP_forMenu = -1">
      <DisplayName>メニュー補足表示用消費MP</DisplayName>
      <Description>メニュー補足表示用消費MP</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>420</SortID>
    </Field>
    <Field Def="dummy8 pad[8]">
      <DisplayName>PAD12</DisplayName>
      <Description>PAD12</Description>
      <SortID>10154</SortID>
    </Field>
  </Fields>
</PARAMDEF>