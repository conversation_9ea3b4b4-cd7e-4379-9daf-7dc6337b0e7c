#!/usr/bin/env python3
"""
语言切换功能测试
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QLabel
from PySide6.QtCore import Qt

from src.utils.language_manager import language_manager, tr


class LanguageTestWindow(QWidget):
    """语言测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
        # 连接语言切换信号
        language_manager.language_changed.connect(self.update_language)
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("语言切换测试")
        self.setFixedSize(400, 300)
        
        layout = QVBoxLayout()
        
        # 标题
        self.title_label = QLabel()
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;")
        
        # 当前语言显示
        self.current_lang_label = QLabel()
        self.current_lang_label.setAlignment(Qt.AlignCenter)
        
        # 测试文本
        self.test_labels = []
        test_keys = [
            ("virtual_lan_title", "虚拟局域网"),
            ("room_name", "房间名称"),
            ("room_password", "房间密码"),
            ("start_network", "启动网络"),
            ("stop_network", "停止网络"),
            ("share_room", "分享房间"),
        ]
        
        for key, default in test_keys:
            label = QLabel()
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("margin: 5px; padding: 5px; border: 1px solid #ccc;")
            self.test_labels.append((label, key, default))
            layout.addWidget(label)
        
        # 语言切换按钮
        btn_layout = QVBoxLayout()
        
        self.chinese_btn = QPushButton("切换到中文")
        self.chinese_btn.clicked.connect(lambda: self.switch_language("zh_CN"))
        
        self.english_btn = QPushButton("Switch to English")
        self.english_btn.clicked.connect(lambda: self.switch_language("en_US"))
        
        btn_layout.addWidget(self.chinese_btn)
        btn_layout.addWidget(self.english_btn)
        
        # 添加到主布局
        layout.addWidget(self.title_label)
        layout.addWidget(self.current_lang_label)
        layout.addStretch()
        layout.addLayout(btn_layout)
        
        self.setLayout(layout)
        
        # 初始化文本
        self.update_language()
    
    def switch_language(self, language_code: str):
        """切换语言"""
        language_manager.set_language(language_code)
    
    def update_language(self):
        """更新语言显示"""
        try:
            current_lang = language_manager.get_current_language()
            
            # 更新标题
            self.title_label.setText(tr("app_title", "Nmodm - 艾尔登法环联机工具"))
            
            # 更新当前语言显示
            lang_name = tr("language_chinese" if current_lang == "zh_CN" else "language_english")
            self.current_lang_label.setText(f"当前语言 / Current Language: {lang_name}")
            
            # 更新测试文本
            for label, key, default in self.test_labels:
                text = tr(key, default)
                label.setText(f"{key}: {text}")
            
            print(f"界面语言已更新为: {current_lang}")
            
        except Exception as e:
            print(f"更新语言失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("Language Test")
    app.setApplicationVersion("1.0.0")
    
    # 创建测试窗口
    window = LanguageTestWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
