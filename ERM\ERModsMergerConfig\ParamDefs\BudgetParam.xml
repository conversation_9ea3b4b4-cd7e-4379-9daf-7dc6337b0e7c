﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>BUDGET_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 vram_all = 1">
      <DisplayName>VRAM:ALL</DisplayName>
      <Description>VRAM:ALL(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="f32 vram_mapobj_tex = 1">
      <DisplayName>VRAM:マップ/オブジェ テクスチャ</DisplayName>
      <Description>VRAM:マップ/オブジェ テクスチャ(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2010</SortID>
    </Field>
    <Field Def="f32 vram_mapobj_mdl = 1">
      <DisplayName>VRAM:マップ/オブジェ モデル</DisplayName>
      <Description>VRAM:マップ/オブジェ モデル(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2020</SortID>
    </Field>
    <Field Def="f32 vram_map = 1">
      <DisplayName>VRAM:マップ</DisplayName>
      <Description>VRAM:マップ(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1010</SortID>
    </Field>
    <Field Def="f32 vram_chr = 1">
      <DisplayName>VRAM:キャラ</DisplayName>
      <Description>VRAM:キャラ(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1020</SortID>
    </Field>
    <Field Def="f32 vram_parts = 1">
      <DisplayName>VRAM:パーツ</DisplayName>
      <Description>VRAM:パーツ(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1030</SortID>
    </Field>
    <Field Def="f32 vram_sfx = 1">
      <DisplayName>VRAM:SFX</DisplayName>
      <Description>VRAM:SFX(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1040</SortID>
    </Field>
    <Field Def="f32 vram_chr_tex = 1">
      <DisplayName>VRAM:キャラ テクスチャ</DisplayName>
      <Description>VRAM:キャラ テクスチャ(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2030</SortID>
    </Field>
    <Field Def="f32 vram_chr_mdl = 1">
      <DisplayName>VRAM:キャラ モデル</DisplayName>
      <Description>VRAM:キャラ モデル(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2040</SortID>
    </Field>
    <Field Def="f32 vram_parts_tex = 1">
      <DisplayName>VRAM:パーツ テクスチャ</DisplayName>
      <Description>VRAM:パーツ テクスチャ(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2050</SortID>
    </Field>
    <Field Def="f32 vram_parts_mdl = 1">
      <DisplayName>VRAM:パーツ モデル</DisplayName>
      <Description>VRAM:パーツ モデル(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2060</SortID>
    </Field>
    <Field Def="f32 vram_sfx_tex = 1">
      <DisplayName>VRAM:SFX テクスチャ</DisplayName>
      <Description>VRAM:SFX テクスチャ(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2070</SortID>
    </Field>
    <Field Def="f32 vram_sfx_mdl = 1">
      <DisplayName>VRAM:SFX モデル</DisplayName>
      <Description>VRAM:SFX モデル(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2080</SortID>
    </Field>
    <Field Def="f32 vram_gi = 1">
      <DisplayName>VRAM:Gi</DisplayName>
      <Description>VRAM:Gi(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2090</SortID>
    </Field>
    <Field Def="f32 vram_menu_tex = 1">
      <DisplayName>VRAM:メニュー</DisplayName>
      <Description>VRAM:メニュー(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2085</SortID>
    </Field>
    <Field Def="f32 vram_decal_rt = 1">
      <DisplayName>VRAM:DECAL_RT</DisplayName>
      <Description>VRAM:DECALレンダーターゲット(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2105</SortID>
    </Field>
    <Field Def="f32 vram_decal = 1">
      <DisplayName>VRAM:DECAL</DisplayName>
      <Description>VRAM:DECAL(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="dummy8 reserve_0[4]">
      <DisplayName>予約領域</DisplayName>
      <DisplayFormat>%f</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>3041</SortID>
    </Field>
    <Field Def="f32 vram_other_tex = 1">
      <DisplayName>VRAM:その他 テクスチャ</DisplayName>
      <Description>VRAM:その他 モデル(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2110</SortID>
    </Field>
    <Field Def="f32 vram_other_mdl = 1">
      <DisplayName>VRAM:その他 モデル</DisplayName>
      <Description>VRAM:その他 テクスチャ(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2120</SortID>
    </Field>
    <Field Def="f32 havok_anim = 1">
      <DisplayName>HAVOK:アニメ</DisplayName>
      <Description>HAVOK:アニメ(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3010</SortID>
    </Field>
    <Field Def="f32 havok_ins = 1">
      <DisplayName>HAVOK:配置</DisplayName>
      <Description>HAVOK:配置(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3020</SortID>
    </Field>
    <Field Def="f32 havok_hit = 1">
      <DisplayName>HAVOK:ヒット</DisplayName>
      <Description>HAVOK:ヒット(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3030</SortID>
    </Field>
    <Field Def="f32 vram_other = 1">
      <DisplayName>VRAM:その他</DisplayName>
      <Description>VRAM:その他(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1050</SortID>
    </Field>
    <Field Def="f32 vram_detail_all = 1">
      <DisplayName>VRAM:合算値</DisplayName>
      <Description>VRAM:合算値(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="f32 vram_chr_and_parts = 1">
      <DisplayName>VRAM:キャラ&amp;パーツ</DisplayName>
      <Description>VRAM:キャラとパーツ合算値(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1035</SortID>
    </Field>
    <Field Def="f32 havok_navimesh = 1">
      <DisplayName>HAVOK:ナビメッシュ</DisplayName>
      <Description>HAVOK:ナビメッシュ(単位はMB)</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3040</SortID>
    </Field>
    <Field Def="dummy8 reserve_1[24]">
      <DisplayName>予約領域</DisplayName>
      <Description>予約領域</Description>
      <SortID>3042</SortID>
    </Field>
  </Fields>
</PARAMDEF>