name: 创建发行版

on:
  workflow_dispatch:
    inputs:
      version:
        description: '发行版本号 (例如: v1.0.0)'
        required: true
        default: 'v1.0.0'
      prerelease:
        description: '是否为预发布版本'
        required: false
        default: false
        type: boolean
      draft:
        description: '是否为草稿版本'
        required: false
        default: false
        type: boolean

permissions:
  contents: write
  actions: read

jobs:
  create-release:
    runs-on: windows-latest

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置 Python 环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install nuitka

    - name: 创建必要目录
      run: |
        New-Item -ItemType Directory -Force -Path "OnlineFix"
        New-Item -ItemType Directory -Force -Path "Mods"
        "OnlineFix64.dll" | Out-File -FilePath "OnlineFix/dlllist.txt" -Encoding UTF8

    - name: 使用 Nuitka 打包 (目录版)
      run: |
        # 创建简化的构建脚本避免编码问题
        python -c "
        import sys
        import os
        import subprocess
        import shutil
        from pathlib import Path

        # 设置环境变量避免编码问题
        os.environ['PYTHONIOENCODING'] = 'utf-8'

        # 简化的 Nuitka 构建
        project_root = Path('.')
        builds_dir = project_root / 'Builds'
        dist_dir = builds_dir / 'Nuitka'

        # 确保目录存在
        builds_dir.mkdir(exist_ok=True)
        dist_dir.mkdir(exist_ok=True)

        # 清理旧文件
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
            dist_dir.mkdir(exist_ok=True)

        # Nuitka 命令
        cmd = [
            sys.executable, '-m', 'nuitka',
            '--assume-yes-for-downloads',
            '--enable-plugin=pyside6',
            '--disable-console',
            '--standalone',
            '--output-dir=' + str(dist_dir),
            '--output-filename=Nmodm_nuitka_standalone',
            '--include-data-file=zwnr.png=zwnr.png',
            '--include-data-dir=OnlineFix=OnlineFix',
            '--include-data-dir=src=src',
            'main.py'
        ]

        print('Starting Nuitka build...')
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)

        if result.returncode == 0:
            print('Nuitka build completed successfully')

            # 重命名输出目录
            main_dist = dist_dir / 'main.dist'
            target_dir = dist_dir / 'Nmodm_nuitka_standalone'
            if main_dist.exists() and not target_dir.exists():
                main_dist.rename(target_dir)
                print('Output directory renamed to Nmodm_nuitka_standalone')
        else:
            print('Nuitka build failed:')
            print(result.stderr)
            sys.exit(1)
        "

    - name: 创建发布包
      run: |
        New-Item -ItemType Directory -Force -Path "release"

        # 查找 Nuitka 构建输出目录
        $nuitkaDir = $null
        if (Test-Path "Builds/Nuitka/Nmodm_nuitka_standalone") {
          $nuitkaDir = "Builds/Nuitka/Nmodm_nuitka_standalone"
        } elseif (Test-Path "Builds/Nuitka/main.dist") {
          $nuitkaDir = "Builds/Nuitka/main.dist"
        }

        if ($nuitkaDir) {
          Write-Host "Found Nuitka build directory: $nuitkaDir"
          Compress-Archive -Path "$nuitkaDir/*" -DestinationPath "release/Nmodm-v${{ github.event.inputs.version }}.zip"
          Write-Host "Main release package created: Nmodm-v${{ github.event.inputs.version }}.zip"
        } else {
          Write-Host "Nuitka build output not found"
          Get-ChildItem "Builds" -Recurse | Write-Host
          exit 1
        }

        # 创建源代码包
        $sourceFiles = @("src", "OnlineFix", "main.py", "requirements.txt", "README.md", "LICENSE", "build_manager.py", "install_dependencies.py")
        $existingFiles = @()
        foreach ($file in $sourceFiles) {
          if (Test-Path $file) {
            $existingFiles += $file
          }
        }
        if ($existingFiles.Count -gt 0) {
          Compress-Archive -Path $existingFiles -DestinationPath "release/Nmodm-Source-v${{ github.event.inputs.version }}.zip"
          Write-Host "Source code package created: Nmodm-Source-v${{ github.event.inputs.version }}.zip"
        }
        
    - name: 创建发行版
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ github.event.inputs.version }}
        name: Nmodm ${{ github.event.inputs.version }}
        body: |
          # Nmodm ${{ github.event.inputs.version }} Release

          ## New Version Release

          This is Nmodm - Modern ME3 Mod Manager version ${{ github.event.inputs.version }}.

          ## Download Options

          ### Main Version (Recommended)
          - **Nmodm-v${{ github.event.inputs.version }}.zip** - Nuitka directory build, high performance

          ### Developer Version
          - **Nmodm-Source-v${{ github.event.inputs.version }}.zip** - Source code package, requires Python environment

          ## Main Features

          - Modern frameless GUI interface
          - Intelligent Mod management system
          - Complete ME3 configuration and launch
          - Multi-mirror download support
          - Mod annotation and classification
          - External Mod support
          - Multi-theme interface

          ## System Requirements

          - Windows 10/11 (64-bit)
          - Elden Ring Nightreign game

          ## Usage Instructions

          ### Main Version
          1. Download `Nmodm-v${{ github.event.inputs.version }}.zip`
          2. Extract to any directory
          3. Run `Nmodm.exe`
          4. Follow the in-app guide to configure game path and mods

          ### Source Code Version
          1. Download and extract source code package
          2. Install Python 3.11+
          3. Run `python install_dependencies.py`
          4. Run `python main.py`

          ## Important Notes

          - Please backup game saves before use
          - Some antivirus software may report false positives, please add to trust list
          - Ensure you own a legal copy of the game

          ## Bug Reports

          If you encounter issues, please report them on the [Issues](https://github.com/QykXczj/Nmodm/issues) page.

          ## Acknowledgments

          Thanks to all users for their support and feedback!
        draft: ${{ github.event.inputs.draft }}
        prerelease: ${{ github.event.inputs.prerelease }}
        files: |
          release/*
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
