﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>BULLET_PARAM_ST</ParamType>
  <DataVersion>4</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="s32 atkId_Bullet = -1">
      <DisplayName>攻撃ID</DisplayName>
      <Description>攻撃パラメータのＩＤをそれぞれ登録する.→攻撃タイプ／攻撃材質／物理攻撃力／魔法攻撃力／スタミナ攻撃力／ノックバック距離.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="s32 sfxId_Bullet = -1">
      <DisplayName>SFXID【弾】</DisplayName>
      <Description>【弾】用SFX IDを入れる。-1は発生しない。</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="s32 sfxId_Hit = -1">
      <DisplayName>SFXID【着弾】</DisplayName>
      <Description>【着弾】SFXIDを入れる。-1は発生しない。</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>3000</SortID>
    </Field>
    <Field Def="s32 sfxId_Flick = -1">
      <DisplayName>SFXID【はじき時】</DisplayName>
      <Description>【はじき時】SFXIDを入れる。-1は発生しない。</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>4000</SortID>
    </Field>
    <Field Def="f32 life = -1">
      <DisplayName>寿命[s]</DisplayName>
      <Description>飛び道具が存在し続けられる時間（-1なら無限）.</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999</Maximum>
      <SortID>29000</SortID>
    </Field>
    <Field Def="f32 dist">
      <DisplayName>射程距離[m]</DisplayName>
      <Description>減衰が開始される距離（実際の飛距離ではない）.</Description>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>0.1</Increment>
      <SortID>33000</SortID>
    </Field>
    <Field Def="f32 shootInterval">
      <DisplayName>発射間隔[s]</DisplayName>
      <Description>飛び道具を何秒間隔で発射するかを指定.</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>28000</SortID>
    </Field>
    <Field Def="f32 gravityInRange">
      <DisplayName>射程距離内重力[m/s^2]</DisplayName>
      <Description>射程距離内での下向きにかかる重力.</Description>
      <Minimum>-999</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>34000</SortID>
    </Field>
    <Field Def="f32 gravityOutRange">
      <DisplayName>射程距離外重力[m/s^2]</DisplayName>
      <Description>減衰がはじまったときの下向きにかかる重力（ポトンと落ちる感じを表現.</Description>
      <Minimum>-999</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>35000</SortID>
    </Field>
    <Field Def="f32 hormingStopRange">
      <DisplayName>誘導停止距離[m]</DisplayName>
      <Description>誘導を停止するターゲットとの距離。誘導弾で当たり過ぎないようにするパラメータ。</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>45000</SortID>
    </Field>
    <Field Def="f32 initVellocity">
      <DisplayName>初速[m/s]</DisplayName>
      <Description>ＳＦＸの初速度.</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>36000</SortID>
    </Field>
    <Field Def="f32 accelInRange">
      <DisplayName>射程距離内加速度[m/s^2]</DisplayName>
      <Description>ＳＦＸの射程内の加速度.</Description>
      <Minimum>-999</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>40000</SortID>
    </Field>
    <Field Def="f32 accelOutRange">
      <DisplayName>射程距離外加速度[m/s^2]</DisplayName>
      <Description>ＳＦＸが射程距離外に出たときの加速度.</Description>
      <Minimum>-999</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>41000</SortID>
    </Field>
    <Field Def="f32 maxVellocity">
      <DisplayName>最高速度[m/s]</DisplayName>
      <Description>最高速度.</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>38000</SortID>
    </Field>
    <Field Def="f32 minVellocity">
      <DisplayName>最低速度[m/s]</DisplayName>
      <Description>最低保証速度.</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>37000</SortID>
    </Field>
    <Field Def="f32 accelTime">
      <DisplayName>加速開始時間[s]</DisplayName>
      <Description>この時間までは、加速しない（ロケット弾みたいな魔法を撃つことができるようにしておく）.</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>39000</SortID>
    </Field>
    <Field Def="f32 homingBeginDist">
      <DisplayName>誘導開始距離[m]</DisplayName>
      <Description>何ｍ進んだ地点から誘導を開始するか.</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>44000</SortID>
    </Field>
    <Field Def="f32 hitRadius = -1">
      <DisplayName>初期弾半径[m]</DisplayName>
      <Description>当たり球の半径を設定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>30000</SortID>
    </Field>
    <Field Def="f32 hitRadiusMax = -1">
      <DisplayName>最大弾半径[m]</DisplayName>
      <Description>あたり球の最大半径（－1の場合、初期半径と同じにする／デフォルト）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>31000</SortID>
    </Field>
    <Field Def="f32 spreadTime">
      <DisplayName>範囲拡散時間[s]</DisplayName>
      <Description>範囲半径が細大にまで広がる時間.</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>32000</SortID>
    </Field>
    <Field Def="f32 expDelay">
      <DisplayName>発動遅延[s]</DisplayName>
      <Description>着弾後、爆発までの時間（０の場合はすぐに爆発）.</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>59000</SortID>
    </Field>
    <Field Def="f32 hormingOffsetRange">
      <DisplayName>誘導ずらし量[m]</DisplayName>
      <Description>０だと正確。射撃時にXYZ各成分を、この量だけずらして狙うようにする。</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>46000</SortID>
    </Field>
    <Field Def="f32 dmgHitRecordLifeTime">
      <DisplayName>ダメージヒット履歴の生存時間[s]</DisplayName>
      <Description>ダメージヒット履歴の生存時間[sec](&lt;=0.0f：無期限)</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>17000</SortID>
    </Field>
    <Field Def="f32 externalForce">
      <DisplayName>外力[m/s^2]</DisplayName>
      <Description>射撃時の方向にかかる外力.(Y軸は抜いている)</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>35500</SortID>
    </Field>
    <Field Def="s32 spEffectIDForShooter = -1">
      <DisplayName>射撃した人にかける特殊効果</DisplayName>
      <Description>射撃した人にかける特殊効果</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>47000</SortID>
    </Field>
    <Field Def="s32 autoSearchNPCThinkID">
      <DisplayName>ファンネルNPC思考ID</DisplayName>
      <Description>ファンネルがターゲットの検索使用するパラメータ</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>60000</SortID>
    </Field>
    <Field Def="s32 HitBulletID = -1">
      <DisplayName>発生弾丸ID</DisplayName>
      <Description>弾丸パラメータから、新しく弾丸パラメータを発生させるときにＩＤを指定する</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>11000</SortID>
    </Field>
    <Field Def="s32 spEffectId0 = -1">
      <DisplayName>特殊効果ID0</DisplayName>
      <Description>特殊効果パラメータのＩＤをそれぞれ登録する.→特殊効果全般.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>48000</SortID>
    </Field>
    <Field Def="s32 spEffectId1 = -1">
      <DisplayName>特殊効果ID1</DisplayName>
      <Description>特殊効果パラメータのＩＤをそれぞれ登録する.→特殊効果全般.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>49000</SortID>
    </Field>
    <Field Def="s32 spEffectId2 = -1">
      <DisplayName>特殊効果ID2</DisplayName>
      <Description>特殊効果パラメータのＩＤをそれぞれ登録する.→特殊効果全般.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50000</SortID>
    </Field>
    <Field Def="s32 spEffectId3 = -1">
      <DisplayName>特殊効果ID3</DisplayName>
      <Description>特殊効果パラメータのＩＤをそれぞれ登録する.→特殊効果全般.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>51000</SortID>
    </Field>
    <Field Def="s32 spEffectId4 = -1">
      <DisplayName>特殊効果ID4</DisplayName>
      <Description>特殊効果パラメータのＩＤをそれぞれ登録する.→特殊効果全般.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>52000</SortID>
    </Field>
    <Field Def="u16 numShoot">
      <DisplayName>発射数</DisplayName>
      <Description>一度に発射する飛び道具の数.</Description>
      <Maximum>99</Maximum>
      <SortID>23000</SortID>
    </Field>
    <Field Def="s16 homingAngle">
      <DisplayName>誘導性能[deg/s]</DisplayName>
      <Description>1秒間に何度まで補正するか？.</Description>
      <Minimum>0</Minimum>
      <SortID>43000</SortID>
    </Field>
    <Field Def="s16 shootAngle">
      <DisplayName>発射角度[deg]</DisplayName>
      <Description>飛び道具を前方何度に向かって発射するかを指定.</Description>
      <Minimum>-360</Minimum>
      <Maximum>360</Maximum>
      <SortID>24000</SortID>
    </Field>
    <Field Def="s16 shootAngleInterval">
      <DisplayName>発射角度間隔[deg]</DisplayName>
      <Description>飛び道具を複数発射する場合、何度間隔で発射するかを指定.(Y軸)</Description>
      <Minimum>-360</Minimum>
      <Maximum>360</Maximum>
      <SortID>25000</SortID>
    </Field>
    <Field Def="s16 shootAngleXInterval">
      <DisplayName>発射仰角間隔[deg]</DisplayName>
      <Description>飛び道具を複数発射する場合、何度間隔で発射するかを指定.(X軸)</Description>
      <Minimum>-360</Minimum>
      <Maximum>360</Maximum>
      <SortID>27000</SortID>
    </Field>
    <Field Def="s8 damageDamp">
      <DisplayName>物理攻撃力減衰率[%/s]</DisplayName>
      <Description>減衰距離以降、1秒間に減少する補正値.</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>53000</SortID>
    </Field>
    <Field Def="s8 spelDamageDamp">
      <DisplayName>魔法攻撃力減衰率[%/s]</DisplayName>
      <Description>減衰距離以降、1秒間に減少する補正値.</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>54000</SortID>
    </Field>
    <Field Def="s8 fireDamageDamp">
      <DisplayName>炎攻撃力減衰率[%/s]</DisplayName>
      <Description>減衰距離以降、1秒間に減少する補正値.</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>55000</SortID>
    </Field>
    <Field Def="s8 thunderDamageDamp">
      <DisplayName>電撃攻撃力減衰率[%/s]</DisplayName>
      <Description>減衰距離以降、1秒間に減少する補正値.</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>56000</SortID>
    </Field>
    <Field Def="s8 staminaDamp">
      <DisplayName>スタミナダメージ減衰率[%/s]</DisplayName>
      <Description>減衰距離以降、1秒間に減少する補正値.</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>57000</SortID>
    </Field>
    <Field Def="s8 knockbackDamp">
      <DisplayName>ノックバック減衰率[%/s]</DisplayName>
      <Description>減衰距離以降、1秒間に減少する補正値.</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>58000</SortID>
    </Field>
    <Field Def="s8 shootAngleXZ">
      <DisplayName>発射仰角[deg]</DisplayName>
      <Description>水平方向からの追加仰角。</Description>
      <Minimum>-90</Minimum>
      <Maximum>90</Maximum>
      <SortID>26000</SortID>
    </Field>
    <Field Def="u8 lockShootLimitAng">
      <DisplayName>ロック方向制限角度</DisplayName>
      <Description>ロック方向を向かせるときの制限角度</Description>
      <Maximum>180</Maximum>
      <SortID>42000</SortID>
    </Field>
    <Field Def="dummy8 pad2[1]">
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>99201</SortID>
    </Field>
    <Field Def="u8 prevVelocityDirRate">
      <DisplayName>前回の移動方向加算率[%]</DisplayName>
      <Description>滑る弾が壁にヒット時に前回の移動方向を今の方向へ加算する比率</Description>
      <Maximum>100</Maximum>
      <SortID>46500</SortID>
    </Field>
    <Field Def="u8 atkAttribute = 254">
      <DisplayName>物理属性</DisplayName>
      <Enum>ATKPARAM_ATKATTR_TYPE</Enum>
      <Description>弾丸に設定する物理属性を設定</Description>
      <SortID>6000</SortID>
    </Field>
    <Field Def="u8 spAttribute = 254">
      <DisplayName>特殊属性</DisplayName>
      <Enum>ATKPARAM_SPATTR_TYPE</Enum>
      <Description>弾丸に設定する特殊属性を設定</Description>
      <SortID>7000</SortID>
    </Field>
    <Field Def="u8 Material_AttackType = 254">
      <DisplayName>攻撃属性[SFX/SE]</DisplayName>
      <Enum>BEHAVIOR_ATK_TYPE</Enum>
      <Description>攻撃属性が何かを指定する</Description>
      <SortID>8000</SortID>
    </Field>
    <Field Def="u8 Material_AttackMaterial = 254">
      <DisplayName>攻撃材質[SFX/SE]</DisplayName>
      <Enum>WEP_MATERIAL_ATK</Enum>
      <Description>攻撃時のSFX/ＳＥに使用</Description>
      <SortID>9000</SortID>
    </Field>
    <Field Def="u8 isPenetrateChr:1">
      <DisplayName>キャラを貫通？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>ONであればキャラに当たったときに着弾せず貫通する</Description>
      <Maximum>1</Maximum>
      <SortID>19000</SortID>
    </Field>
    <Field Def="u8 isPenetrateObj:1">
      <DisplayName>オブジェを貫通？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>ONであれば動的/部分破壊アセットに当たったときに着弾せず貫通する</Description>
      <Maximum>1</Maximum>
      <SortID>19100</SortID>
    </Field>
    
    <Field Def="dummy8 pad_old:6" RemovedVersion="11210015" />
    
    <Field Def="u8 unknown_0x98_3:1" FirstVersion="11210015" />
    <Field Def="dummy8 pad:5" FirstVersion="11210015" >
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>99202</SortID>
    </Field>
    
    <Field Def="u8 launchConditionType">
      <DisplayName>発生条件</DisplayName>
      <Enum>BULLET_LAUNCH_CONDITION_TYPE</Enum>
      <Description>着弾・寿命消滅時に弾を発生させるか判定する条件を指定</Description>
      <SortID>12000</SortID>
    </Field>
    <Field Def="u8 FollowType:3">
      <DisplayName>追従タイプ</DisplayName>
      <Enum>BULLET_FOLLOW_TYPE</Enum>
      <Description>追従タイプ。「追従しない」がデフォルト。</Description>
      <SortID>14000</SortID>
    </Field>
    <Field Def="u8 EmittePosType:3">
      <DisplayName>発生源タイプ</DisplayName>
      <Enum>BULLET_EMITTE_POS_TYPE</Enum>
      <Description>発生源タイプ。ダミポリからが通常。（メテオを判定するために導入）</Description>
      <Maximum>7</Maximum>
      <SortID>13000</SortID>
    </Field>
    <Field Def="u8 isAttackSFX:1">
      <DisplayName>刺さったままになるか</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>矢などの弾丸が、キャラクターに刺さったままになるかどうかを設定する</Description>
      <Maximum>1</Maximum>
      <SortID>5000</SortID>
    </Field>
    <Field Def="u8 isEndlessHit:1">
      <DisplayName>あたり続けるか？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>あたり続けるか？</Description>
      <Maximum>1</Maximum>
      <SortID>15000</SortID>
    </Field>
    <Field Def="u8 isPenetrateMap:1">
      <DisplayName>マップを貫通？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>ONであればヒット/静的アセットに当たったときに着弾せず貫通する</Description>
      <Maximum>1</Maximum>
      <SortID>20000</SortID>
    </Field>
    <Field Def="u8 isHitBothTeam:1">
      <DisplayName>敵味方共にあたる？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>敵味方共にあたるか？（徘徊ゴーストにはあたらない）</Description>
      <Maximum>1</Maximum>
      <SortID>21000</SortID>
    </Field>
    <Field Def="u8 isUseSharedHitList:1">
      <DisplayName>ヒットリストを共有するか？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>ヒットリストを共有するかを指定</Description>
      <Maximum>1</Maximum>
      <SortID>16000</SortID>
    </Field>
    <Field Def="u8 isUseMultiDmyPolyIfPlace:1">
      <DisplayName>複数のダミポリを使うか？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>弾配置時に同一ダミポリIDを複数使うか？</Description>
      <Maximum>1</Maximum>
      <SortID>22000</SortID>
    </Field>
    <Field Def="u8 isHitOtherBulletForceEraseA:1">
      <DisplayName>他弾強制消去Aに当たるか</DisplayName>
      <Description>他弾強制消去Aに当たるか</Description>
      <Maximum>1</Maximum>
      <SortID>62200</SortID>
    </Field>
    <Field Def="u8 isHitOtherBulletForceEraseB:1">
      <DisplayName>他弾強制消去Bに当たるか</DisplayName>
      <Description>他弾強制消去Bに当たるか</Description>
      <Maximum>1</Maximum>
      <SortID>62300</SortID>
    </Field>
    <Field Def="u8 isHitForceMagic:1">
      <DisplayName>フォース魔法に当たるか</DisplayName>
      <Description>フォース魔法に当たるか</Description>
      <Maximum>1</Maximum>
      <SortID>62000</SortID>
    </Field>
    <Field Def="u8 isIgnoreSfxIfHitWater:1">
      <DisplayName>水面衝突時のエフェクト無視するか</DisplayName>
      <Description>水面に当たった場合はエフェクト無視するか</Description>
      <Maximum>1</Maximum>
      <SortID>63000</SortID>
    </Field>
    <Field Def="u8 isIgnoreMoveStateIfHitWater:1">
      <DisplayName>水面衝突時の状態遷移を無視するか</DisplayName>
      <Description>水に当たっても状態遷移を無視するか</Description>
      <Maximum>1</Maximum>
      <SortID>64000</SortID>
    </Field>
    <Field Def="u8 isHitDarkForceMagic:1">
      <DisplayName>闇フォース魔法に当たるか</DisplayName>
      <Description>闇フォース魔法に当たるか</Description>
      <Maximum>1</Maximum>
      <SortID>62000</SortID>
    </Field>
    <Field Def="u8 dmgCalcSide:2">
      <DisplayName>ダメージ計算サイド</DisplayName>
      <Enum>DMG_CALC_SIDE_TYPE</Enum>
      <Description>ダメージ計算サイド。　マルチプレイ時に、ダメージの計算を、与えた側 or 受けた側を切り替えるためのもの。</Description>
      <Maximum>1</Maximum>
      <SortID>70000</SortID>
    </Field>
    <Field Def="u8 isEnableAutoHoming:1">
      <DisplayName>弾丸自動捕捉許可</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>非ロックオン時に自動追従するか</Description>
      <Maximum>1</Maximum>
      <SortID>14500</SortID>
    </Field>
    <Field Def="u8 isSyncBulletCulcDumypolyPos:1">
      <DisplayName>同期弾丸の場合、ダミポリ位置での再計算を行うか</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>同期生成された弾丸の場合、弾丸生成時にダミポリ位置による姿勢の再計算を行わず、同期時のエミッタ姿勢を使用する。</Description>
      <Maximum>1</Maximum>
      <SortID>14600</SortID>
    </Field>
    <Field Def="u8 isOwnerOverrideInitAngle:1">
      <DisplayName>弾丸の基準方向をオーナーに上書きするか？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>子弾丸のみ有効。ONなら基準方向をオーナーにする。</Description>
      <Maximum>1</Maximum>
      <SortID>42500</SortID>
    </Field>
    <Field Def="u8 isInheritSfxToChild:1">
      <DisplayName>子弾にSFXを引き継ぐか</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>親弾のSFXを引き継ぐ。子弾に設定されたSFXIDは無視する </Description>
      <Maximum>1</Maximum>
      <SortID>12500</SortID>
    </Field>
    <Field Def="s8 darkDamageDamp">
      <DisplayName>闇攻撃力減衰率[%/s]</DisplayName>
      <Description>減衰距離以降、1秒間に減少する補正値.</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>56500</SortID>
    </Field>
    <Field Def="s8 bulletSfxDeleteType_byHit">
      <DisplayName>着弾時の弾丸SFX消滅タイプ</DisplayName>
      <Enum>BULLET_SFX_DELETE_TYPE</Enum>
      <Description>着弾or弾き時の弾丸SFX消滅タイプ</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>5500</SortID>
    </Field>
    <Field Def="s8 bulletSfxDeleteType_byLifeDead">
      <DisplayName>寿命時の弾丸SFX消滅タイプ</DisplayName>
      <Enum>BULLET_SFX_DELETE_TYPE</Enum>
      <Description>寿命時の弾丸SFX消滅タイプ</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>5600</SortID>
    </Field>
    <Field Def="f32 targetYOffsetRange">
      <DisplayName>目標上下オフセット[m]</DisplayName>
      <Description>着弾位置の上下オフセット。発射時とホーミング中のターゲット位置を上下にずらす。（-n～n）</Description>
      <Minimum>-999</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>46100</SortID>
    </Field>
    <Field Def="f32 shootAngleYMaxRandom">
      <DisplayName>発射角度乱数[deg]</DisplayName>
      <Description>発射角度乱数の上限（0～360）</Description>
      <Minimum>0</Minimum>
      <Maximum>360</Maximum>
      <SortID>25100</SortID>
    </Field>
    <Field Def="f32 shootAngleXMaxRandom">
      <DisplayName>発射仰角乱数[deg]</DisplayName>
      <Description>発射仰角乱数の上限（0～360）</Description>
      <Minimum>0</Minimum>
      <Maximum>360</Maximum>
      <SortID>27100</SortID>
    </Field>
    <Field Def="s32 intervalCreateBulletId = -1">
      <DisplayName>間隔指定発生弾丸ID</DisplayName>
      <Description>一定間隔で弾丸を作る時に使う、弾丸のID</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>80000</SortID>
    </Field>
    <Field Def="f32 intervalCreateTimeMin">
      <DisplayName>発生間隔：最小時間[s]</DisplayName>
      <Description>一定間隔で弾丸を作る間隔の最小（0～n）</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <SortID>81000</SortID>
    </Field>
    <Field Def="f32 intervalCreateTimeMax">
      <DisplayName>発生間隔：最大時間[s]</DisplayName>
      <Description>一定間隔で弾丸を作る間隔の最大（0～n 0なら機能無効）</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <SortID>82000</SortID>
    </Field>
    <Field Def="f32 predictionShootObserveTime">
      <DisplayName>予測射撃の速度観測時間[s]</DisplayName>
      <Description>予測射撃機能の平均速度観測時間（0～4 0なら機能無効）</Description>
      <Minimum>0</Minimum>
      <Maximum>4</Maximum>
      <SortID>90000</SortID>
    </Field>
    <Field Def="f32 intervalCreateWaitTime">
      <DisplayName>間隔指定発生開始待ち時間[s]</DisplayName>
      <Description>一定間隔で弾丸を作り始めるまでの待ち時間</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <SortID>83000</SortID>
    </Field>
    <Field Def="u8 sfxPostureType">
      <DisplayName>弾丸から発生したSFXの姿勢のタイプ</DisplayName>
      <Enum>BULLET_SFX_CREATE_POSTURE_TYPE</Enum>
      <Description>弾丸から作成されたSFXまたは子弾丸の初期姿勢を設定する</Description>
      <Maximum>2</Maximum>
      <SortID>95000</SortID>
    </Field>
    <Field Def="u8 createLimitGroupId">
      <DisplayName>作成制限グループId</DisplayName>
      <Description>0なら未使用。同一のグループIdに設定された弾丸を作成するときに上限に達していたらその弾丸を作成しない。（ネットワークで同期作成された弾は関係なく出る）</Description>
      <SortID>96000</SortID>
    </Field>
    <Field Def="dummy8 pad5[1]">
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>99203</SortID>
    </Field>
    <Field Def="u8 isInheritSpeedToChild:1">
      <DisplayName>子弾に速度を引き継ぐか</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>子弾に差し替わるタイミングの速度を引き継ぐ。子弾に設定された速度は無視する</Description>
      <Maximum>1</Maximum>
      <SortID>12600</SortID>
    </Field>
    <Field Def="u8 isDisableHitSfx_byChrAndObj:1">
      <DisplayName>キャラ・OBJヒット時着弾SFXを再生しない</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>ONの時、キャラクター/オブジェクトに着弾しても弾丸パラメータの「着弾SFX」を再生しない</Description>
      <Maximum>1</Maximum>
      <SortID>20500</SortID>
    </Field>
    <Field Def="u8 isCheckWall_byCenterRay:1">
      <DisplayName>発射位置壁めり込み判定をキャラ中心を平行に結ぶレイを飛ばして行う</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>弾丸発射時めり込み判定に不具合があったので、それのエラーハンドリング用。SEQ23101 【自キャラ】ロックオン位置の高いキャラに密着してソウルの短矢、強いソウルの短矢を使うと弾丸の方向が反転する</Description>
      <Maximum>1</Maximum>
      <SortID>99000</SortID>
    </Field>
    <Field Def="u8 isHitFlare:1">
      <DisplayName>フレア魔法に当たるか</DisplayName>
      <Description>フレア魔法に当たるか</Description>
      <Maximum>1</Maximum>
      <SortID>62100</SortID>
    </Field>
    <Field Def="u8 isUseBulletWallFilter:1">
      <DisplayName>原始魔法アタリを使うか？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>原始魔法アタリを使うか？原始魔法専用アタリに当たるフィルタに変わります。</Description>
      <Maximum>1</Maximum>
      <SortID>20100</SortID>
    </Field>
    
    <Field Def="dummy8 pad1:1" RemovedVersion="11210015" />
    
    <Field Def="u8 unknown_0xc3_5:1" FirstVersion="11210015" >
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>99204</SortID>
    </Field>
    
    <Field Def="u8 isNonDependenceMagicForFunnleNum:1">
      <DisplayName>PCのファンネル数が理力で変動しない</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>PCのファンネル数が理力で変動しない。発射数になる</Description>
      <Maximum>1</Maximum>
      <SortID>60100</SortID>
    </Field>
    <Field Def="u8 isAiInterruptShootNoDamageBullet:1">
      <DisplayName>AI弾丸反応するか（攻撃力0でも）</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>AI弾丸反応するか（攻撃力0でも）</Description>
      <Maximum>1</Maximum>
      <SortID>21100</SortID>
    </Field>
    <Field Def="f32 randomCreateRadius">
      <DisplayName>ランダム発生時の発生範囲(半径)[m]</DisplayName>
      <Description>発生源タイプがランダムな位置に発生する設定の場合に利用される、弾丸の発生範囲。</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13500</SortID>
    </Field>
    <Field Def="f32 followOffset_BaseHeight">
      <DisplayName>ファンネル追従位置_基点高さ[m]</DisplayName>
      <Description>ファンネル追従位置_基点高さ[m]</Description>
      <Minimum>-999</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>60200</SortID>
    </Field>
    <Field Def="s32 assetNo_Hit = -1">
      <DisplayName>着弾時に発生するアセット番号</DisplayName>
      <Description>着弾時に発生させるアセットの番号。-1：生成しない。アセット番号はアセットの下6桁の数値です。例： AEG999_999 = 999999</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>4501</SortID>
      <UnkC8 />
    </Field>
    <Field Def="f32 lifeRandomRange">
      <DisplayName>寿命乱数[s]</DisplayName>
      <Description>「寿命[s]」に対して、設定した時間の振れ幅を持つ乱数秒を加える</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <SortID>29100</SortID>
    </Field>
    <Field Def="s16 homingAngleX = -1">
      <DisplayName>誘導性能（X軸個別）[deg/s]</DisplayName>
      <Description>誘導性能のX軸成分だけを変えます。-1で変えません</Description>
      <Minimum>-1</Minimum>
      <SortID>43500</SortID>
    </Field>
    <Field Def="u8 ballisticCalcType">
      <DisplayName>弾道計算タイプ</DisplayName>
      <Enum>BULLET_BALLISTIC_CALC_TYPE</Enum>
      <Description>弾道計算タイプ</Description>
      <Maximum>1</Maximum>
      <SortID>22500</SortID>
    </Field>
    <Field Def="u8 attachEffectType">
      <DisplayName>アタッチ効果タイプ</DisplayName>
      <Enum>BULLET_ATTACH_EFFECT_TYPE</Enum>
      <Description>アタッチする効果タイプ</Description>
      <Maximum>5</Maximum>
      <SortID>61000</SortID>
    </Field>
    <Field Def="s32 seId_Bullet1 = -1">
      <DisplayName>SEID1【弾】</DisplayName>
      <Description>【弾】用SE ID1 を入れる。-1：生成しない　9桁。サウンドタイプはs：SFX固定。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>4100</SortID>
    </Field>
    <Field Def="s32 seId_Bullet2 = -1">
      <DisplayName>SEID2【弾】</DisplayName>
      <Description>【弾】用SE ID2 を入れる。-1：生成しない　9桁。サウンドタイプはs：SFX固定。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>4101</SortID>
    </Field>
    <Field Def="s32 seId_Hit = -1">
      <DisplayName>SEID【着弾】</DisplayName>
      <Description>【着弾】用SE ID1 を入れる。-1は発生しない。 9桁。サウンドタイプはs：SFX固定。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>4102</SortID>
    </Field>
    <Field Def="s32 seId_Flick = -1">
      <DisplayName>SEID【はじき時】</DisplayName>
      <Description>【はじき時】用SE ID1 を入れる。-1は発生しない。 9桁。サウンドタイプはs：SFX固定。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>4103</SortID>
    </Field>
    <Field Def="s16 howitzerShootAngleXMin">
      <DisplayName>【曲射】発射仰角制限_下限[deg]</DisplayName>
      <Description>【曲射】曲射計算の適用前の発射仰角を基準(0deg)とした下限[deg]。</Description>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <SortID>27500</SortID>
    </Field>
    <Field Def="s16 howitzerShootAngleXMax">
      <DisplayName>【曲射】発射仰角制限_上限[deg]</DisplayName>
      <Description>【曲射】曲射計算の適用前の発射仰角を基準(0deg)とした上限[deg]。</Description>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <SortID>27501</SortID>
    </Field>
    <Field Def="f32 howitzerInitMinVelocity">
      <DisplayName>【曲射】最低制限速度[m/s]</DisplayName>
      <Description>【曲射】曲射計算の最低制限速度[m/s]。</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>41500</SortID>
    </Field>
    <Field Def="f32 howitzerInitMaxVelocity">
      <DisplayName>【曲射】最高制限速度[m/s]</DisplayName>
      <Description>【曲射】曲射計算の最高制限速度[m/s]。</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>41501</SortID>
    </Field>
    <Field Def="s32 sfxId_ForceErase = -1">
      <DisplayName>SFXID【強制消去時】</DisplayName>
      <Description>強制消去時SFXID。-1は発生しない。</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>4100</SortID>
    </Field>
    <Field Def="s8 bulletSfxDeleteType_byForceErase">
      <DisplayName>強制消去時の弾丸SFX消滅タイプ</DisplayName>
      <Enum>BULLET_SFX_DELETE_TYPE</Enum>
      <Description>強制消去時の弾丸SFX消滅タイプ</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>5610</SortID>
    </Field>
    <Field Def="dummy8 pad3[1]">
      <DisplayName>パディング3</DisplayName>
      <Description>pad3</Description>
      <SortID>99205</SortID>
    </Field>
    <Field Def="s16 followDmypoly_forSfxPose = -1">
      <DisplayName>追従時SFX方向指定ダミポリ</DisplayName>
      <Description>追従時SFX方向指定ダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>14200</SortID>
    </Field>
    <Field Def="f32 followOffset_Radius">
      <DisplayName>ファンネル追従位置_半径[m]</DisplayName>
      <Description>ファンネル追従位置_半径[m]</Description>
      <Minimum>-999</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>60300</SortID>
    </Field>
    <Field Def="f32 spBulletDistUpRate = 1">
      <DisplayName>特殊効果飛距離補正倍率</DisplayName>
      <Description>特殊効果飛距離補正倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>99200</SortID>
    </Field>
    <Field Def="f32 nolockTargetDist">
      <DisplayName>非ロック時ターゲット射程距離[m]</DisplayName>
      <Description>非ロック時ターゲット射程距離（-1：「射程距離」を参照する、0：ターゲットなし）</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999</Maximum>
      <Increment>0.1</Increment>
      <SortID>33500</SortID>
    </Field>
    <Field Def="dummy8 pad4[8]">
      <DisplayName>pad</DisplayName>
      <SortID>99206</SortID>
    </Field>
  </Fields>
</PARAMDEF>