﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>REINFORCE_PARAM_WEAPON_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 physicsAtkRate = 1">
      <DisplayName>物理攻撃力基本値</DisplayName>
      <Description>物理攻撃力の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="f32 magicAtkRate = 1">
      <DisplayName>魔法攻撃力基本値</DisplayName>
      <Description>魔法攻撃力の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="f32 fireAtkRate = 1">
      <DisplayName>炎攻撃力基本値</DisplayName>
      <Description>炎攻撃力の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="f32 thunderAtkRate = 1">
      <DisplayName>電撃攻撃力基本値</DisplayName>
      <Description>電撃攻撃力の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="f32 staminaAtkRate = 1">
      <DisplayName>スタミナ攻撃力</DisplayName>
      <Description>スタミナ攻撃力の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="f32 saWeaponAtkRate = 1">
      <DisplayName>SA武器攻撃力</DisplayName>
      <Description>スーパーアーマー武器攻撃色の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>600</SortID>
    </Field>
    <Field Def="f32 saDurabilityRate = 1">
      <DisplayName>SA耐久値</DisplayName>
      <Description>SA耐久力の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="f32 correctStrengthRate = 1">
      <DisplayName>筋力補正</DisplayName>
      <Description>筋力補正の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="f32 correctAgilityRate = 1">
      <DisplayName>俊敏補正</DisplayName>
      <Description>俊敏補正の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="f32 correctMagicRate = 1">
      <DisplayName>魔力補正</DisplayName>
      <Description>魔力補正の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="f32 correctFaithRate = 1">
      <DisplayName>信仰補正</DisplayName>
      <Description>信仰補正の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1100</SortID>
    </Field>
    <Field Def="f32 physicsGuardCutRate = 1">
      <DisplayName>ガード時物理攻撃カット率</DisplayName>
      <Description>ガード時物理攻撃カット率の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="f32 magicGuardCutRate = 1">
      <DisplayName>ガード時魔法攻撃カット率</DisplayName>
      <Description>ガード時魔法攻撃カット率の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1210</SortID>
    </Field>
    <Field Def="f32 fireGuardCutRate = 1">
      <DisplayName>ガード時炎攻撃カット率</DisplayName>
      <Description>ガード時炎攻撃カット率の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1220</SortID>
    </Field>
    <Field Def="f32 thunderGuardCutRate = 1">
      <DisplayName>ガード時電撃攻撃カット率</DisplayName>
      <Description>ガード時電撃攻撃カット率の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1230</SortID>
    </Field>
    <Field Def="f32 poisonGuardResistRate = 1">
      <DisplayName>ガード時毒攻撃カット率</DisplayName>
      <Description>ガード時毒攻撃カット率の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1300</SortID>
    </Field>
    <Field Def="f32 diseaseGuardResistRate = 1">
      <DisplayName>ガード時疫病攻撃カット率</DisplayName>
      <Description>ガード時疫病攻撃カット率の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1310</SortID>
    </Field>
    <Field Def="f32 bloodGuardResistRate = 1">
      <DisplayName>ガード時出血攻撃カット率</DisplayName>
      <Description>ガード時出血攻撃カット率の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1320</SortID>
    </Field>
    <Field Def="f32 curseGuardResistRate = 1">
      <DisplayName>ガード時呪攻撃カット率</DisplayName>
      <Description>ガード時呪い攻撃カット率の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1330</SortID>
    </Field>
    <Field Def="f32 staminaGuardDefRate = 1">
      <DisplayName>ガード時スタミナ防御力</DisplayName>
      <Description>ガード時スタミナ防御力の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="u8 spEffectId1">
      <DisplayName>特殊効果ID1</DisplayName>
      <Description>特殊効果ID1の加算補正値</Description>
      <SortID>2100</SortID>
    </Field>
    <Field Def="u8 spEffectId2">
      <DisplayName>特殊効果ID2</DisplayName>
      <Description>特殊効果ID2の加算補正値</Description>
      <SortID>2200</SortID>
    </Field>
    <Field Def="u8 spEffectId3">
      <DisplayName>特殊効果ID3</DisplayName>
      <Description>特殊効果ID3の加算補正値</Description>
      <SortID>2300</SortID>
    </Field>
    <Field Def="u8 residentSpEffectId1">
      <DisplayName>常駐特殊効果ID1</DisplayName>
      <Description>常駐特殊効果ID1の加算補正値</Description>
      <SortID>2400</SortID>
    </Field>
    <Field Def="u8 residentSpEffectId2">
      <DisplayName>常駐特殊効果ID2</DisplayName>
      <Description>常駐特殊効果ID2の加算補正値</Description>
      <SortID>2500</SortID>
    </Field>
    <Field Def="u8 residentSpEffectId3">
      <DisplayName>常駐特殊効果ID3</DisplayName>
      <Description>常駐特殊効果ID3の加算補正値</Description>
      <SortID>2600</SortID>
    </Field>
    <Field Def="u8 materialSetId">
      <DisplayName>素材ID加算値</DisplayName>
      <Description>素材パラメータIDの加算補正値</Description>
      <SortID>2700</SortID>
    </Field>
    <Field Def="u8 maxReinforceLevel">
      <DisplayName>最大強化武器レベル用レベル値</DisplayName>
      <Description>最大強化武器レベル用レベル値</Description>
      <Maximum>99</Maximum>
      <SortID>3000</SortID>
    </Field>
    <Field Def="f32 darkAtkRate = 1">
      <DisplayName>闇攻撃力基本値</DisplayName>
      <Description>闇攻撃力の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>410</SortID>
    </Field>
    <Field Def="f32 darkGuardCutRate = 1">
      <DisplayName>ガード時闇攻撃カット率</DisplayName>
      <Description>ガード時闇攻撃カット率の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1240</SortID>
    </Field>
    <Field Def="f32 correctLuckRate = 1">
      <DisplayName>運補正</DisplayName>
      <Description>運補正の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1150</SortID>
    </Field>
    <Field Def="f32 freezeGuardDefRate = 1">
      <DisplayName>ガード時冷気攻撃カット率</DisplayName>
      <Description>ガード時冷気攻撃カット率の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1340</SortID>
    </Field>
    <Field Def="f32 reinforcePriceRate = 1">
      <DisplayName>強化価格補正値</DisplayName>
      <Description>武器パラメータの強化価格に乗算する補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>2800</SortID>
    </Field>
    <Field Def="f32 baseChangePriceRate = 1">
      <DisplayName>進化価格補正値</DisplayName>
      <Description>武器パラメータの進化価格に乗算する補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>2900</SortID>
    </Field>
    <Field Def="s8 enableGemRank">
      <DisplayName>装着可能魔石ランク</DisplayName>
      <Description>装着可能魔石ランク</Description>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>3100</SortID>
    </Field>
    <Field Def="dummy8 pad2[3]">
      <DisplayName>pad</DisplayName>
      <SortID>3101</SortID>
    </Field>
    <Field Def="f32 sleepGuardDefRate = 1">
      <DisplayName>ガード時睡眠攻撃カット率</DisplayName>
      <Description>ガード時睡眠攻撃カット率の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1350</SortID>
    </Field>
    <Field Def="f32 madnessGuardDefRate = 1">
      <DisplayName>ガード時発狂攻撃カット率</DisplayName>
      <Description>ガード時発狂攻撃カット率の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>1360</SortID>
    </Field>
    <Field Def="f32 baseAtkRate = 1">
      <DisplayName>加算攻撃力倍率</DisplayName>
      <Description>加算攻撃力倍率</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>450</SortID>
    </Field>
  </Fields>
</PARAMDEF>