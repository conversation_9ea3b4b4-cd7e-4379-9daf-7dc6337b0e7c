#!/usr/bin/env python3
"""
打包优化测试脚本
验证移除ESL目录打包后的效果
"""

import os
import sys
import tempfile
import zipfile
import shutil
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def analyze_current_structure():
    """分析当前项目结构"""
    print("📁 当前项目结构分析...")
    print("=" * 50)
    
    project_root = Path(__file__).parent
    
    # 检查关键目录
    directories = {
        "OnlineFix": project_root / "OnlineFix",
        "ESL": project_root / "ESL", 
        "ESR": project_root / "ESR",
        "src": project_root / "src"
    }
    
    for name, path in directories.items():
        if path.exists():
            if path.is_dir():
                file_count = len(list(path.rglob("*")))
                size_mb = sum(f.stat().st_size for f in path.rglob("*") if f.is_file()) / (1024 * 1024)
                print(f"✅ {name}: {file_count} 个文件, {size_mb:.2f} MB")
            else:
                print(f"❌ {name}: 不是目录")
        else:
            print(f"❌ {name}: 不存在")
    
    # 检查关键文件
    key_files = {
        "esl2.zip": project_root / "OnlineFix" / "esl2.zip",
        "tool.zip": project_root / "OnlineFix" / "tool.zip"
    }
    
    print("\n📦 关键压缩包:")
    for name, path in key_files.items():
        if path.exists():
            size_mb = path.stat().st_size / (1024 * 1024)
            print(f"✅ {name}: {size_mb:.2f} MB")
        else:
            print(f"❌ {name}: 不存在")


def calculate_packaging_size_reduction():
    """计算打包大小减少"""
    print("\n📊 打包大小优化分析...")
    print("=" * 50)
    
    project_root = Path(__file__).parent
    
    # 计算ESL目录大小
    esl_dir = project_root / "ESL"
    if esl_dir.exists():
        esl_files = list(esl_dir.rglob("*"))
        esl_file_count = len([f for f in esl_files if f.is_file()])
        esl_size_mb = sum(f.stat().st_size for f in esl_files if f.is_file()) / (1024 * 1024)
        
        print(f"📁 ESL目录统计:")
        print(f"   文件数量: {esl_file_count}")
        print(f"   总大小: {esl_size_mb:.2f} MB")
        
        # 分析文件类型
        file_types = {}
        for file_path in esl_files:
            if file_path.is_file():
                ext = file_path.suffix.lower()
                if ext not in file_types:
                    file_types[ext] = {"count": 0, "size": 0}
                file_types[ext]["count"] += 1
                file_types[ext]["size"] += file_path.stat().st_size
        
        print(f"\n   文件类型分布:")
        for ext, info in sorted(file_types.items(), key=lambda x: x[1]["size"], reverse=True):
            size_mb = info["size"] / (1024 * 1024)
            print(f"   {ext or '无扩展名'}: {info['count']} 个文件, {size_mb:.2f} MB")
    else:
        esl_size_mb = 0
        print("❌ ESL目录不存在")
    
    # 计算OnlineFix中esl2.zip大小
    esl_zip = project_root / "OnlineFix" / "esl2.zip"
    if esl_zip.exists():
        esl_zip_size_mb = esl_zip.stat().st_size / (1024 * 1024)
        print(f"\n📦 esl2.zip大小: {esl_zip_size_mb:.2f} MB")
        
        if esl_size_mb > 0:
            reduction_mb = esl_size_mb - esl_zip_size_mb
            reduction_percent = (reduction_mb / esl_size_mb) * 100
            print(f"\n💾 打包大小优化:")
            print(f"   优化前: {esl_size_mb:.2f} MB (ESL目录)")
            print(f"   优化后: {esl_zip_size_mb:.2f} MB (esl2.zip)")
            print(f"   减少: {reduction_mb:.2f} MB ({reduction_percent:.1f}%)")
        else:
            print(f"\n💾 打包大小: {esl_zip_size_mb:.2f} MB (仅esl2.zip)")
    else:
        print("❌ esl2.zip不存在")


def test_packaging_scripts():
    """测试打包脚本配置"""
    print("\n🔧 打包脚本配置测试...")
    print("=" * 50)
    
    project_root = Path(__file__).parent
    
    # 测试Nuitka脚本
    nuitka_script = project_root / "build_nuitka.py"
    if nuitka_script.exists():
        print("📝 检查Nuitka打包脚本...")
        
        with open(nuitka_script, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查ESL目录引用
        if "ESL" in content and "include-data-file" in content:
            # 查找具体的ESL引用
            lines = content.split('\n')
            esl_references = []
            for i, line in enumerate(lines, 1):
                if "ESL" in line and ("include" in line.lower() or "data" in line.lower()):
                    esl_references.append(f"第{i}行: {line.strip()}")
            
            if esl_references:
                print("⚠️ 发现ESL目录引用:")
                for ref in esl_references:
                    print(f"   {ref}")
            else:
                print("✅ 未发现ESL目录打包引用")
        else:
            print("✅ 未发现ESL目录打包引用")
    else:
        print("❌ Nuitka打包脚本不存在")
    
    # 测试PyInstaller脚本
    pyinstaller_script = project_root / "build_pyinstaller.py"
    if pyinstaller_script.exists():
        print("\n📝 检查PyInstaller打包脚本...")
        
        with open(pyinstaller_script, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查ESL目录引用
        if "('ESL'" in content:
            print("⚠️ 发现ESL目录打包引用")
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if "('ESL'" in line:
                    print(f"   第{i}行: {line.strip()}")
        else:
            print("✅ 未发现ESL目录打包引用")
    else:
        print("❌ PyInstaller打包脚本不存在")


def simulate_runtime_behavior():
    """模拟运行时行为"""
    print("\n🎮 运行时行为模拟...")
    print("=" * 50)
    
    # 创建临时环境模拟打包后的情况
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 模拟打包后的目录结构（没有ESL目录）
        onlinefix_dir = temp_path / "OnlineFix"
        onlinefix_dir.mkdir()
        
        # 复制esl2.zip到临时目录
        project_root = Path(__file__).parent
        source_esl_zip = project_root / "OnlineFix" / "esl2.zip"
        
        if source_esl_zip.exists():
            target_esl_zip = onlinefix_dir / "esl2.zip"
            shutil.copy2(source_esl_zip, target_esl_zip)
            print(f"✅ 模拟环境已创建: {temp_path}")
            print(f"   OnlineFix/esl2.zip: {target_esl_zip.exists()}")
            print(f"   ESL目录: {(temp_path / 'ESL').exists()}")
            
            # 模拟ESL初始化过程
            print("\n🔍 模拟ESL初始化过程...")
            
            # 模拟路径设置
            esl_dir = temp_path / "ESL"
            esl_extracted_flag = esl_dir / ".esl_extracted"
            
            print(f"1. 检查解压标志: {esl_extracted_flag.exists()}")
            print(f"2. 检查OnlineFix/esl2.zip: {target_esl_zip.exists()}")
            
            if target_esl_zip.exists():
                print("3. 开始模拟解压...")
                
                # 创建ESL目录
                esl_dir.mkdir()
                
                # 模拟解压过程
                try:
                    with zipfile.ZipFile(target_esl_zip, 'r') as zip_ref:
                        zip_ref.extractall(temp_path)
                    
                    # 创建解压标志
                    import time
                    esl_extracted_flag.write_text(f"ESL extracted at {time.strftime('%Y-%m-%d %H:%M:%S')}")
                    
                    print("✅ 模拟解压成功")
                    print(f"   ESL目录已创建: {esl_dir.exists()}")
                    print(f"   解压标志已创建: {esl_extracted_flag.exists()}")
                    print(f"   原压缩包保留: {target_esl_zip.exists()}")
                    
                    # 检查关键文件
                    key_files = [
                        "steamclient_loader.exe",
                        "steam_settings"
                    ]
                    
                    print("\n   关键文件检查:")
                    for file_name in key_files:
                        file_path = esl_dir / file_name
                        print(f"   {file_name}: {file_path.exists()}")
                    
                except Exception as e:
                    print(f"❌ 模拟解压失败: {e}")
            else:
                print("❌ esl2.zip不存在，无法模拟")
        else:
            print("❌ 源esl2.zip不存在，无法创建模拟环境")


def analyze_benefits():
    """分析优化收益"""
    print("\n💡 优化收益分析...")
    print("=" * 50)
    
    benefits = [
        {
            "category": "打包大小",
            "items": [
                "减少ESL目录的重复打包",
                "压缩包比解压文件更小",
                "减少安装包体积"
            ]
        },
        {
            "category": "维护便利",
            "items": [
                "统一的压缩包管理",
                "避免文件版本不一致",
                "简化打包配置"
            ]
        },
        {
            "category": "用户体验",
            "items": [
                "更快的下载速度",
                "按需解压，节省磁盘空间",
                "自动处理文件更新"
            ]
        },
        {
            "category": "开发效率",
            "items": [
                "减少打包时间",
                "简化部署流程",
                "统一的文件管理策略"
            ]
        }
    ]
    
    for benefit in benefits:
        print(f"🎯 {benefit['category']}:")
        for item in benefit['items']:
            print(f"   ✅ {item}")
        print()


def main():
    """主函数"""
    print("🎯 打包优化测试")
    print("=" * 60)
    
    # 分析当前结构
    analyze_current_structure()
    
    # 计算大小减少
    calculate_packaging_size_reduction()
    
    # 测试打包脚本
    test_packaging_scripts()
    
    # 模拟运行时行为
    simulate_runtime_behavior()
    
    # 分析优化收益
    analyze_benefits()
    
    print("📋 总结:")
    print("=" * 60)
    print("✅ ESL目录已从打包配置中移除")
    print("✅ 运行时从OnlineFix/esl2.zip解压")
    print("✅ 打包大小得到优化")
    print("✅ 维护便利性提升")
    print("✅ 用户体验改善")
    
    print("\n💡 建议:")
    print("• 确保OnlineFix/esl2.zip包含最新的ESL工具")
    print("• 测试打包后的程序运行是否正常")
    print("• 验证ESL工具的解压和初始化流程")
    print("• 考虑清理开发环境中的ESL目录")


if __name__ == "__main__":
    main()
