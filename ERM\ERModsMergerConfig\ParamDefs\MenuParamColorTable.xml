﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>MENU_PARAM_COLOR_TABLE_ST</ParamType>
  <DataVersion>2</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 lerpMode">
      <DisplayName>補間方法</DisplayName>
      <Enum>MENU_COLOR_LERP_MODE</Enum>
      <Description>補間方法</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>1</SortID>
    </Field>
    <Field Def="dummy8 pad1[3]">
      <DisplayName>パッド</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>11</SortID>
    </Field>
    <Field Def="u16 h">
      <DisplayName>色相</DisplayName>
      <Description>色相。補間では固定値として扱う</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>359</Maximum>
      <SortID>2</SortID>
    </Field>
    <Field Def="dummy8 pad2[2]">
      <DisplayName>パッド</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>12</SortID>
    </Field>
    <Field Def="f32 s1 = 1">
      <DisplayName>彩度</DisplayName>
      <Description>彩度1。補間の1点目として扱われる</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>3</SortID>
      <UnkC8>1点目</UnkC8>
    </Field>
    <Field Def="f32 v1 = 1">
      <DisplayName>明度</DisplayName>
      <Description>明度1。補間の1点目として扱われる</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>4</SortID>
      <UnkC8>1点目</UnkC8>
    </Field>
    <Field Def="f32 s2 = 1">
      <DisplayName>彩度</DisplayName>
      <Description>彩度2。補間の2点目として扱われる</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>5</SortID>
      <UnkC8>2点目</UnkC8>
    </Field>
    <Field Def="f32 v2 = 1">
      <DisplayName>明度</DisplayName>
      <Description>明度2。補間の2点目として扱われる</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>6</SortID>
      <UnkC8>2点目</UnkC8>
    </Field>
    <Field Def="f32 s3 = 1">
      <DisplayName>彩度</DisplayName>
      <Description>彩度3。補間の3点目として扱われる</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>7</SortID>
      <UnkC8>3点目</UnkC8>
    </Field>
    <Field Def="f32 v3 = 1">
      <DisplayName>明度</DisplayName>
      <Description>明度3。補間の3点目として扱われる</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>8</SortID>
      <UnkC8>3点目</UnkC8>
    </Field>
  </Fields>
</PARAMDEF>