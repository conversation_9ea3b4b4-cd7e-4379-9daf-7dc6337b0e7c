#!/usr/bin/env python3
"""
并行进程查找优化测试脚本
测试新的并行进程查找和终止方法的性能
"""

import time
import sys
import os
import psutil
import concurrent.futures
import subprocess
import csv
from io import StringIO

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


class ProcessOptimizationTester:
    """进程优化测试器"""
    
    def __init__(self):
        self.target_processes = [
            'easytier-core.exe',
            'WinIPBroadcast.exe',
            'python.exe',  # 添加一些常见进程用于测试
            'notepad.exe',
            'explorer.exe'
            # KCP工具已移除，因为EasyTier自带KCP支持
        ]
    
    def test_original_method(self):
        """测试原始的进程查找方法"""
        print("🔍 测试原始方法...")
        start_time = time.time()
        
        found_processes = {}
        for process_name in self.target_processes:
            found_processes[process_name] = []

        for proc in psutil.process_iter(['pid', 'name']):
            try:
                proc_name = proc.info['name']
                if proc_name in self.target_processes:
                    found_processes[proc_name].append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
            except Exception:
                continue
        
        end_time = time.time()
        total_found = sum(len(procs) for procs in found_processes.values())
        
        return {
            'method': '原始方法',
            'time': end_time - start_time,
            'found_count': total_found,
            'processes': found_processes
        }
    
    def test_tasklist_method(self):
        """测试 tasklist 命令方法"""
        print("🔍 测试 tasklist 方法...")
        start_time = time.time()
        
        found_processes = {}
        for process_name in self.target_processes:
            found_processes[process_name] = []
        
        try:
            for process_name in self.target_processes:
                processes = self._find_process_by_tasklist(process_name)
                found_processes[process_name] = processes
        except Exception as e:
            print(f"tasklist 方法失败: {e}")
        
        end_time = time.time()
        total_found = sum(len(procs) for procs in found_processes.values())
        
        return {
            'method': 'tasklist 方法',
            'time': end_time - start_time,
            'found_count': total_found,
            'processes': found_processes
        }
    
    def test_parallel_tasklist_method(self):
        """测试并行 tasklist 方法"""
        print("🔍 测试并行 tasklist 方法...")
        start_time = time.time()
        
        found_processes = {}
        for process_name in self.target_processes:
            found_processes[process_name] = []
        
        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=len(self.target_processes)) as executor:
                future_to_process = {
                    executor.submit(self._find_process_by_tasklist, process_name): process_name
                    for process_name in self.target_processes
                }
                
                for future in concurrent.futures.as_completed(future_to_process, timeout=10):
                    try:
                        process_name = future_to_process[future]
                        processes = future.result()
                        found_processes[process_name] = processes
                    except Exception as e:
                        process_name = future_to_process[future]
                        print(f"查找进程 {process_name} 失败: {e}")
        except Exception as e:
            print(f"并行 tasklist 方法失败: {e}")
        
        end_time = time.time()
        total_found = sum(len(procs) for procs in found_processes.values())
        
        return {
            'method': '并行 tasklist 方法',
            'time': end_time - start_time,
            'found_count': total_found,
            'processes': found_processes
        }
    
    def test_parallel_psutil_method(self):
        """测试并行 psutil 方法"""
        print("🔍 测试并行 psutil 方法...")
        start_time = time.time()
        
        found_processes = {}
        for process_name in self.target_processes:
            found_processes[process_name] = []
        
        try:
            # 一次性获取所有进程信息
            all_processes = list(psutil.process_iter(['pid', 'name']))
            
            # 分批处理
            def check_process_batch(process_batch):
                batch_results = {}
                for process_name in self.target_processes:
                    batch_results[process_name] = []
                
                for proc in process_batch:
                    try:
                        proc_name = proc.info['name']
                        if proc_name in self.target_processes:
                            batch_results[proc_name].append(proc)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                
                return batch_results
            
            # 将进程列表分批
            batch_size = max(1, len(all_processes) // 4)
            process_batches = [all_processes[i:i + batch_size] 
                             for i in range(0, len(all_processes), batch_size)]
            
            # 并行处理各批次
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                futures = [executor.submit(check_process_batch, batch) 
                          for batch in process_batches]
                
                for future in concurrent.futures.as_completed(futures, timeout=10):
                    try:
                        batch_results = future.result()
                        for process_name, processes in batch_results.items():
                            found_processes[process_name].extend(processes)
                    except Exception as e:
                        print(f"处理批次失败: {e}")
        except Exception as e:
            print(f"并行 psutil 方法失败: {e}")
        
        end_time = time.time()
        total_found = sum(len(procs) for procs in found_processes.values())
        
        return {
            'method': '并行 psutil 方法',
            'time': end_time - start_time,
            'found_count': total_found,
            'processes': found_processes
        }
    
    def _find_process_by_tasklist(self, process_name):
        """使用 tasklist 查找特定进程"""
        processes = []
        try:
            result = subprocess.run([
                'tasklist', '/FI', f'IMAGENAME eq {process_name}', '/FO', 'CSV', '/NH'
            ], capture_output=True, text=True, timeout=3)
            
            if result.returncode == 0 and result.stdout.strip():
                csv_reader = csv.reader(StringIO(result.stdout))
                for row in csv_reader:
                    if len(row) >= 2:
                        try:
                            pid = int(row[1].strip('"'))
                            proc = psutil.Process(pid)
                            if proc.name().lower() == process_name.lower():
                                processes.append(proc)
                        except (ValueError, psutil.NoSuchProcess, psutil.AccessDenied):
                            continue
        except Exception as e:
            print(f"tasklist 查找 {process_name} 失败: {e}")
        
        return processes
    
    def run_performance_test(self):
        """运行性能测试"""
        print("🚀 开始进程查找性能测试...")
        print("=" * 60)
        
        methods = [
            self.test_original_method,
            self.test_parallel_psutil_method,
        ]
        
        # 在 Windows 上添加 tasklist 方法
        if sys.platform == "win32":
            methods.extend([
                self.test_tasklist_method,
                self.test_parallel_tasklist_method,
            ])
        
        results = []
        
        for method in methods:
            try:
                result = method()
                results.append(result)
                print(f"✅ {result['method']}: {result['time']:.3f}秒, 找到 {result['found_count']} 个进程")
            except Exception as e:
                print(f"❌ 测试失败: {e}")
            print()
        
        # 性能对比
        print("📊 性能对比结果:")
        print("=" * 60)
        
        if results:
            # 按时间排序
            results.sort(key=lambda x: x['time'])
            
            fastest = results[0]
            print(f"🏆 最快方法: {fastest['method']} ({fastest['time']:.3f}秒)")
            
            for i, result in enumerate(results):
                if i == 0:
                    speedup = "基准"
                else:
                    speedup = f"{result['time'] / fastest['time']:.2f}x 慢"
                
                print(f"{i+1}. {result['method']}: {result['time']:.3f}秒 ({speedup})")
        
        print()
        print("💡 优化建议:")
        if sys.platform == "win32":
            print("• Windows 系统建议使用并行 tasklist 方法")
            print("• 对于少量目标进程，tasklist 方法更高效")
        print("• 并行 psutil 方法适用于跨平台场景")
        print("• 原始方法仅作为备用方案")


def main():
    """主函数"""
    print("🎯 进程查找并行优化测试")
    print("=" * 60)
    
    tester = ProcessOptimizationTester()
    tester.run_performance_test()
    
    print("\n🎉 测试完成！")
    print("💡 这些优化方法已集成到 MainWindow._cleanup_processes() 中")


if __name__ == "__main__":
    main()
