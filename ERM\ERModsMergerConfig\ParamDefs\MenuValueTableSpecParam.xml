﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>MENU_VALUE_TABLE_SPEC</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="s32 value">
      <DisplayName>比較する値</DisplayName>
      <Description>比較する値</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2</SortID>
    </Field>
    <Field Def="s32 textId">
      <DisplayName>変換後のテキストID</DisplayName>
      <Description>変換後のテキストID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>3</SortID>
    </Field>
    <Field Def="s8 compareType">
      <DisplayName>比較タイプ</DisplayName>
      <Enum>MENU_VALUE_TABLE_CMP_TYPE</Enum>
      <Description>比較タイプ</Description>
      <Minimum>0</Minimum>
      <Maximum>255</Maximum>
      <SortID>1</SortID>
    </Field>
    <Field Def="dummy8 padding[3]">
      <DisplayName>パディング</DisplayName>
      <Description>パディング</Description>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>999</SortID>
    </Field>
  </Fields>
</PARAMDEF>