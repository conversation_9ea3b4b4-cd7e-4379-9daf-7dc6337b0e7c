﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>NETWORK_PARAM_ST</ParamType>
  <DataVersion>10</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 signVerticalOffset">
      <DisplayName>サイン高さオフセット[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <UnkB8>common</UnkB8>
      <UnkC0>NET_COMMON_PARAM</UnkC0>
      <UnkC8>共通</UnkC8>
    </Field>
    <Field Def="f32 maxSignPosCorrectionRange">
      <DisplayName>サイン位置補正最大距離[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>1</SortID>
      <UnkB8>common</UnkB8>
      <UnkC0>NET_COMMON_PARAM</UnkC0>
      <UnkC8>共通</UnkC8>
    </Field>
    <Field Def="f32 summonTimeoutTime">
      <DisplayName>召喚希望タイムアウト時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>1</Increment>
      <SortID>2</SortID>
      <UnkB8>common</UnkB8>
      <UnkC0>NET_COMMON_PARAM</UnkC0>
      <UnkC8>共通</UnkC8>
    </Field>
    <Field Def="dummy8 pad_0[4]">
      <DisplayName>予約</DisplayName>
      <SortID>3</SortID>
      <UnkB8>common</UnkB8>
      <UnkC0>NET_COMMON_PARAM</UnkC0>
      <UnkC8>共通</UnkC8>
    </Field>
    <Field Def="f32 signPuddleActiveMessageIntervalSec = 1">
      <DisplayName>サイン溜まり登録中メッセージ表示間隔[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>100</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 keyGuideHeight_0 = 1">
      <DisplayName>キーガイド垂直範囲[m]</DisplayName>
      <Minimum>1</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>101</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 reloadSignIntervalTime1 = 1">
      <DisplayName>召喚サイン再取得待機時間(過疎時)[秒]</DisplayName>
      <Minimum>1</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>102</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 reloadSignIntervalTime2 = 1">
      <DisplayName>召喚サイン再取得待機時間[秒]</DisplayName>
      <Minimum>1</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>103</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="u32 reloadSignTotalCount_0 = 1">
      <DisplayName>召喚サイン所持可能数上限(全体)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>20</Maximum>
      <SortID>105</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="u32 reloadSignCellCount_0 = 1">
      <DisplayName>召喚サイン所持可能数上限(セル)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>99</Maximum>
      <SortID>106</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 updateSignIntervalTime = 1">
      <DisplayName>召喚サイン更新待機時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>107</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 basicExclusiveRange_0 = 1">
      <DisplayName>召喚サイン間描画排他水平範囲[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>108</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 basicExclusiveHeight_0 = 1">
      <DisplayName>召喚サイン間描画排他垂直範囲[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>109</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 previewChrWaitingTime = 1">
      <DisplayName>召喚サインキャラモデル描画待機時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>110</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 signVisibleRange_0 = 1">
      <DisplayName>召喚サインPC間描画距離[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>112</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="u32 cellGroupHorizontalRange_0 = 1">
      <DisplayName>召喚サイン取得セル範囲(水平)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1</Maximum>
      <SortID>113</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="u32 cellGroupTopRange_0 = 1">
      <DisplayName>召喚サイン取得セル範囲(上方向)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1</Maximum>
      <SortID>114</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="u32 cellGroupBottomRange_0 = 1">
      <DisplayName>召喚サイン取得セル範囲(下方向)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1</Maximum>
      <SortID>115</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 minWhitePhantomLimitTimeScale = 1">
      <DisplayName>白霊サイン表示制限時間下限倍率</DisplayName>
      <Minimum>1</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>116</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 minSmallPhantomLimitTimeScale = 1">
      <DisplayName>小霊サイン表示制限時間下限倍率</DisplayName>
      <Minimum>1</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>117</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 whiteKeywordLimitTimeScale = 1">
      <DisplayName>白霊サインキーワード延長倍率</DisplayName>
      <Minimum>1</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>118</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 smallKeywordLimitTimeScale = 1">
      <DisplayName>小霊サインキーワード延長倍率</DisplayName>
      <Minimum>1</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>119</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 blackKeywordLimitTimeScale = 1">
      <DisplayName>闇霊サインキーワード延長倍率</DisplayName>
      <Minimum>1</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>120</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 dragonKeywordLimitTimeScale = 1">
      <DisplayName>竜霊サインキーワード延長倍率</DisplayName>
      <Minimum>1</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>121</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="u32 singGetMax = 1">
      <DisplayName>サイン取得上限</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1000</Maximum>
      <SortID>122</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 signDownloadSpan = 1">
      <DisplayName>サインダウンロードスパン</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>0.1</Increment>
      <SortID>123</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="f32 signUpdateSpan = 1">
      <DisplayName>サインアップロードスパン</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>0.1</Increment>
      <SortID>124</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="dummy8 signPad[4]">
      <DisplayName>予約</DisplayName>
      <SortID>125</SortID>
      <UnkB8>summonSign</UnkB8>
      <UnkC0>NET_SUMMON_SIGN_PARAM</UnkC0>
      <UnkC8>召喚サイン</UnkC8>
    </Field>
    <Field Def="u32 maxBreakInTargetListCount = 1">
      <DisplayName>乱入先取得数</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>1000</Maximum>
      <SortID>200</SortID>
      <UnkB8>breakIn</UnkB8>
      <UnkC0>NET_BREAKIN_PARAM</UnkC0>
      <UnkC8>乱入</UnkC8>
    </Field>
    <Field Def="f32 breakInRequestIntervalTimeSec = 4">
      <DisplayName>乱入リクエスト間隔[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>201</SortID>
      <UnkB8>breakIn</UnkB8>
      <UnkC0>NET_BREAKIN_PARAM</UnkC0>
      <UnkC8>乱入</UnkC8>
    </Field>
    <Field Def="f32 breakInRequestTimeOutSec = 20">
      <DisplayName>乱入リクエストタイムアウト時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>202</SortID>
      <UnkB8>breakIn</UnkB8>
      <UnkC0>NET_BREAKIN_PARAM</UnkC0>
      <UnkC8>乱入</UnkC8>
    </Field>
    
    <Field Def="dummy8 pad_1_old[4]" RemovedVersion="11210015" />
    
    <Field Def="u8 unknown_0x7c" FirstVersion="11210015" />
    <Field Def="dummy8 pad_1[3]" FirstVersion="11210015" >
      <DisplayName>予約</DisplayName>
      <DisplayFormat>%f</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>203</SortID>
      <UnkB8>breakIn</UnkB8>
      <UnkC0>NET_BREAKIN_PARAM</UnkC0>
      <UnkC8>乱入</UnkC8>
    </Field>
    
    <Field Def="f32 keyGuideRange = 1">
      <DisplayName>キーガイド水平範囲[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>300</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="f32 keyGuideHeight_1 = 1">
      <DisplayName>キーガイド垂直範囲[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>301</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="u32 reloadSignTotalCount_1 = 1">
      <DisplayName>血文字取得数(全体)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>99</Maximum>
      <SortID>302</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="u32 reloadNewSignCellCount = 1">
      <DisplayName>血文字取得数(セル、新順)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>99</Maximum>
      <SortID>303</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="u32 reloadRandomSignCellCount = 1">
      <DisplayName>血文字取得数(セル、ランダム )</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>99</Maximum>
      <SortID>304</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="u32 maxSignTotalCount_0 = 1">
      <DisplayName>血文字所持可能数上限(全体)</DisplayName>
      <Description>本当はu16くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>999</Maximum>
      <SortID>305</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="u32 maxSignCellCount_0 = 1">
      <DisplayName>血文字所持可能数上限(セル)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>100</Maximum>
      <SortID>307</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="f32 basicExclusiveRange_1 = 1">
      <DisplayName>血文字間描画排他水平範囲[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>308</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="f32 basicExclusiveHeight_1 = 1">
      <DisplayName>血文字間描画排他垂直範囲[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>309</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="f32 signVisibleRange_1 = 1">
      <DisplayName>血文字PC間描画距離[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>310</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="u32 maxWriteSignCount = 1">
      <DisplayName>書いた血文字履歴件数上限</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1000</Maximum>
      <SortID>311</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="u32 maxReadSignCount = 1">
      <DisplayName>読んだ血文字履歴件数上限</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1000</Maximum>
      <SortID>312</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="f32 reloadSignIntervalTime_0 = 1">
      <DisplayName>血文字再取得待機時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>313</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="u32 cellGroupHorizontalRange_1 = 1">
      <DisplayName>血文字取得セル範囲(水平)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1</Maximum>
      <SortID>314</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="u32 cellGroupTopRange_1 = 1">
      <DisplayName>血文字取得セル範囲(上方向)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1</Maximum>
      <SortID>315</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="u32 cellGroupBottomRange_1 = 1">
      <DisplayName>血文字取得セル範囲(下方向)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1</Maximum>
      <SortID>316</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="u32 lifeTime_0 = 1">
      <DisplayName>血文字データ保持期間上限[秒]</DisplayName>
      <Description>本当はu16くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>1800</Maximum>
      <SortID>317</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="f32 downloadSpan_0">
      <DisplayName>血文字ダウンロード間隔</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>318</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="f32 downloadEvaluationSpan">
      <DisplayName>血文字評価数ダウンロード間隔</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>318</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="dummy8 pad_2[4]">
      <DisplayName>予約</DisplayName>
      <SortID>319</SortID>
      <UnkB8>bloodMessage</UnkB8>
      <UnkC0>NET_BLOOD_MESSAGE_PARAM</UnkC0>
      <UnkC8>血文字</UnkC8>
    </Field>
    <Field Def="f32 deadingGhostStartPosThreshold = 1">
      <DisplayName>血痕位置と幻影開始位置間の許容距離[m]</DisplayName>
      <Description>血痕位置と幻影開始位置の間の距離がこの値より離れている場合はサーバの登録を行わない</Description>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>0.1</Increment>
      <SortID>401</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="f32 keyGuideHeight_2 = 1">
      <DisplayName>キーガイド垂直範囲[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>402</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="f32 keyGuideRangePlayer = 1">
      <DisplayName>プレイヤー血痕キーガイド水平範囲[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>403</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="f32 keyGuideHeightPlayer = 1">
      <DisplayName>プレイヤー血痕キーガイド垂直範囲[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>404</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="u32 reloadSignTotalCount_2 = 1">
      <DisplayName>血痕取得数(全体)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>99</Maximum>
      <SortID>405</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="u32 reloadSignCellCount_1 = 1">
      <DisplayName>血痕取得数(セル)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>99</Maximum>
      <SortID>406</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="u32 maxSignTotalCount_1 = 1">
      <DisplayName>血痕所持可能数上限(全体)</DisplayName>
      <Description>本当はu16くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>100</Maximum>
      <SortID>407</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="u32 maxSignCellCount_1 = 1">
      <DisplayName>血痕所持可能数上限(セル)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>99</Maximum>
      <SortID>408</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="f32 reloadSignIntervalTime_1 = 1">
      <DisplayName>血痕再取得待機時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>409</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="f32 signVisibleRange_2 = 1">
      <DisplayName>血痕PC間描画距離[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>410</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="f32 basicExclusiveRange_2 = 1">
      <DisplayName>血痕間描画排他水平範囲[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>411</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="f32 basicExclusiveHeight_2 = 1">
      <DisplayName>血痕間描画排他垂直範囲[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>412</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="u32 cellGroupHorizontalRange_2 = 1">
      <DisplayName>血痕取得セル範囲(水平)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1</Maximum>
      <SortID>413</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="u32 cellGroupTopRange_2 = 1">
      <DisplayName>血痕取得セル範囲(上方向)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1</Maximum>
      <SortID>414</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="u32 cellGroupBottomRange_2 = 1">
      <DisplayName>血痕取得セル範囲(下方向)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1</Maximum>
      <SortID>415</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="u32 lifeTime_1 = 1">
      <DisplayName>血痕データ保持期間上限[秒]</DisplayName>
      <Description>本当はu16くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>1800</Maximum>
      <SortID>416</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="f32 recordDeadingGhostTotalTime">
      <DisplayName>死亡幻影記録合計時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>1</Increment>
      <SortID>417</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="f32 recordDeadingGhostMinTime = 5">
      <DisplayName>死亡幻影の最低記録時間[秒]</DisplayName>
      <Description>この記録時間未満の死亡幻影はサーバの登録を行わない</Description>
      <Minimum>0</Minimum>
      <Maximum>40</Maximum>
      <Increment>1</Increment>
      <SortID>418</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="f32 downloadSpan_1">
      <DisplayName>血痕ダウンロード間隔</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>419</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="f32 statueCreatableDistance = 80">
      <DisplayName>石化血痕描画制限距離[m]</DisplayName>
      <Description>オープンフィールド用。石像生成時にPC～生成位置間の距離がこの値以上ならば生成できる</Description>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>420</SortID>
      <UnkB8>bloodstain</UnkB8>
      <UnkC0>NET_BLOODSTAIN_PARAM</UnkC0>
      <UnkC8>血痕</UnkC8>
    </Field>
    <Field Def="u32 reloadGhostTotalCount = 1">
      <DisplayName>幻影取得数(全体)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>99</Maximum>
      <SortID>500</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="u32 reloadGhostCellCount = 1">
      <DisplayName>幻影取得数(セル)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>99</Maximum>
      <SortID>501</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="u32 maxGhostTotalCount = 1">
      <DisplayName>幻影所持可能数上限(全体)</DisplayName>
      <Description>本当はu16くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>10</Maximum>
      <SortID>502</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 distanceOfBeginRecordVersus = 1">
      <DisplayName>敵対PCリプレイ記録開始距離[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>503</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 distanceOfEndRecordVersus = 1">
      <DisplayName>敵対PCリプレイ記録終了距離[m]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>504</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 updateWanderGhostIntervalTime = 1">
      <DisplayName>徘徊幻影アップロード間隔[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>505</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 updateVersusGhostIntervalTime = 1">
      <DisplayName>対戦幻影アップロード間隔[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>506</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 recordWanderingGhostTime = 1">
      <DisplayName>幻影記録時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>40</Maximum>
      <Increment>1</Increment>
      <SortID>507</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 recordWanderingGhostMinTime = 5">
      <DisplayName>徘徊幻影の最低記録時間[秒]</DisplayName>
      <Description>この記録時間未満の徘徊幻影はサーバの登録を行わない</Description>
      <Minimum>0</Minimum>
      <Maximum>40</Maximum>
      <Increment>1</Increment>
      <SortID>508</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 updateBonfireGhostIntervalTime = 1">
      <DisplayName>篝火幻影アップロード間隔[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>509</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 replayGhostRangeOnView = 1">
      <DisplayName>幻影再生距離（視野内）[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>510</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 replayGhostRangeOutView = 1">
      <DisplayName>幻影再生距離（視野外）[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>511</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 replayBonfireGhostTime = 1">
      <DisplayName>篝火幻影再生時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>512</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 minBonfireGhostValidRange = 1">
      <DisplayName>篝火幻影配置最小距離[秒]</DisplayName>
      <Description>篝火からこの距離未満の場所には篝火幻影を配置しない</Description>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>513</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 maxBonfireGhostValidRange = 1">
      <DisplayName>篝火幻影配置最大距離[秒]</DisplayName>
      <Description>篝火からこの距離以上の場所には篝火幻影を配置しない</Description>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>514</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 minReplayIntervalTime = 1">
      <DisplayName>幻影再生間隔下限[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>515</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 maxReplayIntervalTime = 1">
      <DisplayName>幻影再生間隔上限[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>516</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 reloadGhostIntervalTime = 1">
      <DisplayName>幻影定期取得間隔[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>517</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="u32 cellGroupHorizontalRange_3 = 1">
      <DisplayName>幻影取得セル範囲(水平)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1</Maximum>
      <SortID>518</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="u32 cellGroupTopRange_3 = 1">
      <DisplayName>幻影取得セル範囲(上方向)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>1</Maximum>
      <SortID>519</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="s32 replayBonfirePhantomParamIdForCodename">
      <DisplayName>幻影篝火モードファントムパラメータID(コードネーム)</DisplayName>
      <Description>コードネーム一致のときに使われる幻影篝火モードファントムパラメータID</Description>
      <Minimum>0</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>520</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 replayBonfireModeRange = 1">
      <DisplayName>幻影篝火モード再生適用距離</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>521</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="s32 replayBonfirePhantomParamId">
      <DisplayName>幻影篝火モードファントムパラメータID</DisplayName>
      <Description>幻影篝火モードファントムパラメータID</Description>
      <Minimum>0</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>522</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="dummy8 ghostpad[4]">
      <DisplayName>予約</DisplayName>
      <DisplayFormat>%f</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>523</SortID>
      <UnkB8>ghost</UnkB8>
      <UnkC0>NET_GHOST_PARAM</UnkC0>
      <UnkC8>幻影</UnkC8>
    </Field>
    <Field Def="f32 reloadVisitListCoolTime = 1">
      <DisplayName>指輪検索間隔[秒]</DisplayName>
      <Minimum>1</Minimum>
      <Maximum>1000</Maximum>
      <Increment>1</Increment>
      <SortID>600</SortID>
      <UnkB8>visit</UnkB8>
      <UnkC0>NET_VISIT_PARAM</UnkC0>
      <UnkC8>指輪検索</UnkC8>
    </Field>
    <Field Def="u32 maxCoopBlueSummonCount = 1">
      <DisplayName>救援青霊出現数上限</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>255</Maximum>
      <SortID>601</SortID>
      <UnkB8>visit</UnkB8>
      <UnkC0>NET_VISIT_PARAM</UnkC0>
      <UnkC8>指輪検索</UnkC8>
    </Field>
    <Field Def="u32 maxBellGuardSummonCount = 1">
      <DisplayName>鐘守灰霊出現数上限</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>255</Maximum>
      <SortID>602</SortID>
      <UnkB8>visit</UnkB8>
      <UnkC0>NET_VISIT_PARAM</UnkC0>
      <UnkC8>指輪検索</UnkC8>
    </Field>
    <Field Def="u32 maxVisitListCount = 1">
      <DisplayName>指輪検索先取得数</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <Maximum>255</Maximum>
      <SortID>603</SortID>
      <UnkB8>visit</UnkB8>
      <UnkC0>NET_VISIT_PARAM</UnkC0>
      <UnkC8>指輪検索</UnkC8>
    </Field>
    <Field Def="f32 reloadSearch_CoopBlue_Min">
      <DisplayName>救援青霊リロード時間　最小[sec]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>604</SortID>
      <UnkB8>visit</UnkB8>
      <UnkC0>NET_VISIT_PARAM</UnkC0>
      <UnkC8>指輪検索</UnkC8>
    </Field>
    <Field Def="f32 reloadSearch_CoopBlue_Max">
      <DisplayName>救援青霊リロード時間　最大[sec]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>605</SortID>
      <UnkB8>visit</UnkB8>
      <UnkC0>NET_VISIT_PARAM</UnkC0>
      <UnkC8>指輪検索</UnkC8>
    </Field>
    <Field Def="f32 reloadSearch_BellGuard_Min">
      <DisplayName>鐘守リロード時間　最小[sec]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>606</SortID>
      <UnkB8>visit</UnkB8>
      <UnkC0>NET_VISIT_PARAM</UnkC0>
      <UnkC8>指輪検索</UnkC8>
    </Field>
    <Field Def="f32 reloadSearch_BellGuard_Max">
      <DisplayName>鐘守リロード時間　最大[sec]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>607</SortID>
      <UnkB8>visit</UnkB8>
      <UnkC0>NET_VISIT_PARAM</UnkC0>
      <UnkC8>指輪検索</UnkC8>
    </Field>
    <Field Def="f32 reloadSearch_RatKing_Min">
      <DisplayName>ネズミの王リロード時間　最小[sec]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>608</SortID>
      <UnkB8>visit</UnkB8>
      <UnkC0>NET_VISIT_PARAM</UnkC0>
      <UnkC8>指輪検索</UnkC8>
    </Field>
    <Field Def="f32 reloadSearch_RatKing_Max">
      <DisplayName>ネズミの王リロード時間　最大[sec]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>609</SortID>
      <UnkB8>visit</UnkB8>
      <UnkC0>NET_VISIT_PARAM</UnkC0>
      <UnkC8>指輪検索</UnkC8>
    </Field>
    <Field Def="dummy8 visitpad00[8]">
      <DisplayName>予約</DisplayName>
      <SortID>610</SortID>
      <UnkB8>visit</UnkB8>
      <UnkC0>NET_VISIT_PARAM</UnkC0>
      <UnkC8>指輪検索</UnkC8>
    </Field>
    <Field Def="f32 srttMaxLimit = 1000">
      <DisplayName>SRTT上限[ミリ秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>1</Increment>
      <SortID>700</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="f32 srttMeanLimit = 1000">
      <DisplayName>SRTT上限(安定時)[ミリ秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>1</Increment>
      <SortID>701</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="f32 srttMeanDeviationLimit = 1000">
      <DisplayName>RTT平均偏差上限[ミリ秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>1</Increment>
      <SortID>702</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="f32 darkPhantomLimitBoostTime = 1000">
      <DisplayName>闇霊制限時間加速時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>1</Increment>
      <SortID>703</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="f32 darkPhantomLimitBoostScale = 1000">
      <DisplayName>闇霊制限時間加速時倍率</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>1</Increment>
      <SortID>704</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="f32 multiplayDisableLifeTime = 1">
      <DisplayName>マルチプレイ無効化寿命</DisplayName>
      <Minimum>1</Minimum>
      <Maximum>999999</Maximum>
      <Increment>1</Increment>
      <SortID>705</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 abyssMultiplayLimit = 10">
      <DisplayName>深淵霊マルチプレイ回数</DisplayName>
      <Description>深淵エリアで、深淵霊がホストに入ってこれる回数</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Minimum>1</Minimum>
      <SortID>706</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 phantomWarpMinimumTime = 5">
      <DisplayName>霊体がワープするまでの最低時間[秒]</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>707</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 phantomReturnDelayTime = 5">
      <DisplayName>黒水晶使用後に帰還するまでのディレイ時間[秒]</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>708</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 terminateTimeoutTime = 30">
      <DisplayName>切断待ちのタイムアウト時間</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>709</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u16 penaltyPointLanDisconnect">
      <DisplayName>LAN抜きによるペナルティ加算値</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>717</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u16 penaltyPointSignout">
      <DisplayName>サインアウトによるペナルティ加算値</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>718</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u16 penaltyPointReboot">
      <DisplayName>電源断によるペナルティ加算値</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>719</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u16 penaltyPointBeginPenalize">
      <DisplayName>ペナルティが発動するペナルティ値</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>720</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="f32 penaltyForgiveItemLimitTime">
      <DisplayName>「線の理」の販売制限時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>100000000</Maximum>
      <Increment>1</Increment>
      <SortID>725</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 allAreaSearchRate_CoopBlue">
      <DisplayName>全域検索率：救援青霊[0-100]</DisplayName>
      <Description>全域から侵入対象を検索する割合（％）</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>100</Maximum>
      <SortID>730</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 allAreaSearchRate_VsBlue">
      <DisplayName>全域検索率：報復青霊[0-100]</DisplayName>
      <Description>全域から侵入対象を検索する割合（％）</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>100</Maximum>
      <SortID>730</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 allAreaSearchRate_BellGuard">
      <DisplayName>全域検索率：鐘守灰霊[0-100]</DisplayName>
      <Description>全域から侵入対象を検索する割合（％）</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>100</Maximum>
      <SortID>730</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 bloodMessageEvalHealRate = 100">
      <DisplayName>血文字評価時のHP回復割合[0-100]</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>100</Maximum>
      <SortID>735</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u32 smallGoldSuccessHostRewardId">
      <DisplayName>小金霊成功帰還ホスト報酬ID</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>999999</Maximum>
      <SortID>740</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="f32 doorInvalidPlayAreaExtents = 1">
      <DisplayName>ドア付近プレイ領域無効化距離[m]</DisplayName>
      <Description>マルチプレイ領域を区切る黒扉の周辺を、システム的に無効なプレイ領域（-1）にします。その際、無効領域を黒扉のOBJのバウンディングボックスの薄い方を、このパラメータで太らせます。</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>750</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 signDisplayMax = 10">
      <DisplayName>サイン最大同時表示数</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>100</Maximum>
      <SortID>760</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 bloodStainDisplayMax = 7">
      <DisplayName>血痕最大同時表示数</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>100</Maximum>
      <SortID>761</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="u8 bloodMessageDisplayMax = 3">
      <DisplayName>血文字最大同時表示数</DisplayName>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>100</Maximum>
      <SortID>762</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="dummy8 pad00[9]">
      <DisplayName>予約</DisplayName>
      <SortID>763</SortID>
      <UnkB8>extra</UnkB8>
      <UnkC0>NET_EXTRA_PARAM</UnkC0>
      <UnkC8>その他</UnkC8>
    </Field>
    <Field Def="dummy8 pad10[32]">
      <DisplayName>予約</DisplayName>
      <SortID>1560</SortID>
      <UnkB8>VisitorAddition</UnkB8>
      <UnkC0>VISITOR_ADDITION</UnkC0>
      <UnkC8>訪問</UnkC8>
    </Field>
    <Field Def="f32 summonMessageInterval = 1">
      <DisplayName>召喚メッセージが表示間隔[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>0.1</Increment>
      <SortID>1000</SortID>
      <UnkB8>QuickMatch</UnkB8>
      <UnkC0>QUICK_MATCH</UnkC0>
      <UnkC8>クイックマッチ</UnkC8>
    </Field>
    <Field Def="f32 hostRegisterUpdateTime = 1">
      <DisplayName>ホスト定期更新リクエスト間隔[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>0.1</Increment>
      <SortID>1010</SortID>
      <UnkB8>QuickMatch</UnkB8>
      <UnkC0>QUICK_MATCH</UnkC0>
      <UnkC8>クイックマッチ</UnkC8>
    </Field>
    <Field Def="f32 hostTimeOutTime = 1">
      <DisplayName>ホストのゲスト参加待ちタイムアウト時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>0.1</Increment>
      <SortID>1020</SortID>
      <UnkB8>QuickMatch</UnkB8>
      <UnkC0>QUICK_MATCH</UnkC0>
      <UnkC8>クイックマッチ</UnkC8>
    </Field>
    <Field Def="f32 guestUpdateTime = 1">
      <DisplayName>ゲストのホストからの認証待ちタイムアウト時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>0.1</Increment>
      <SortID>1030</SortID>
      <UnkB8>QuickMatch</UnkB8>
      <UnkC0>QUICK_MATCH</UnkC0>
      <UnkC8>クイックマッチ</UnkC8>
    </Field>
    <Field Def="f32 guestPlayerNoTimeOutTime = 1">
      <DisplayName>ゲストPlayNo同期待ちタイムアウト時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>0.1</Increment>
      <SortID>1040</SortID>
      <UnkB8>QuickMatch</UnkB8>
      <UnkC0>QUICK_MATCH</UnkC0>
      <UnkC8>クイックマッチ</UnkC8>
    </Field>
    <Field Def="f32 hostPlayerNoTimeOutTime = 1">
      <DisplayName>ホストPlayNo同期待ちタイムアウト時間[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>0.1</Increment>
      <SortID>1050</SortID>
      <UnkB8>QuickMatch</UnkB8>
      <UnkC0>QUICK_MATCH</UnkC0>
      <UnkC8>クイックマッチ</UnkC8>
    </Field>
    <Field Def="u32 requestSearchQuickMatchLimit = 1">
      <DisplayName>RequestSearchQuickMatchのlimit値</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>255</Maximum>
      <Increment>0</Increment>
      <SortID>1060</SortID>
      <UnkB8>QuickMatch</UnkB8>
      <UnkC0>QUICK_MATCH</UnkC0>
      <UnkC8>クイックマッチ</UnkC8>
    </Field>
    <Field Def="u32 AvatarMatchSearchMax = 1">
      <DisplayName>アバター戦検索時の最大人数(未使用)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>255</Maximum>
      <SortID>1070</SortID>
      <UnkB8>QuickMatch</UnkB8>
      <UnkC0>QUICK_MATCH</UnkC0>
      <UnkC8>クイックマッチ</UnkC8>
    </Field>
    <Field Def="u32 BattleRoyalMatchSearchMin = 1">
      <DisplayName>バトルロイヤル戦検索時の最少人数(未使用)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>255</Maximum>
      <SortID>1080</SortID>
      <UnkB8>QuickMatch</UnkB8>
      <UnkC0>QUICK_MATCH</UnkC0>
      <UnkC8>クイックマッチ</UnkC8>
    </Field>
    <Field Def="u32 BattleRoyalMatchSearchMax = 1">
      <DisplayName>バトルロイヤル戦検索時の最大人数(未使用)</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>255</Maximum>
      <SortID>1090</SortID>
      <UnkB8>QuickMatch</UnkB8>
      <UnkC0>QUICK_MATCH</UnkC0>
      <UnkC8>クイックマッチ</UnkC8>
    </Field>
    <Field Def="dummy8 pad11[8]">
      <DisplayName>予約</DisplayName>
      <SortID>1100</SortID>
      <UnkB8>QuickMatch</UnkB8>
      <UnkC0>QUICK_MATCH</UnkC0>
      <UnkC8>クイックマッチ</UnkC8>
    </Field>
    <Field Def="u32 VisitorListMax = 1">
      <DisplayName>訪問対象者リスト取得最大値</DisplayName>
      <Description>本当はu8くらいで十分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>10000</Maximum>
      <SortID>1500</SortID>
      <UnkB8>Visitor</UnkB8>
      <UnkC0>VISITOR</UnkC0>
      <UnkC8>訪問</UnkC8>
    </Field>
    <Field Def="f32 VisitorTimeOutTime = 1">
      <DisplayName>訪問待ちタイムアウト</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>1520</SortID>
      <UnkB8>Visitor</UnkB8>
      <UnkC0>VISITOR</UnkC0>
      <UnkC8>訪問</UnkC8>
    </Field>
    <Field Def="f32 DownloadSpan_2 = 1">
      <DisplayName>訪問者リストダウンロード間隔[秒]</DisplayName>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>1530</SortID>
      <UnkB8>Visitor</UnkB8>
      <UnkC0>VISITOR</UnkC0>
      <UnkC8>訪問</UnkC8>
    </Field>
    <Field Def="f32 VisitorGuestRequestMessageIntervalSec = 1">
      <DisplayName>訪問先検索メッセージ表示間隔[秒]</DisplayName>
      <Description>訪問ゲストが訪問先を探してる間に出すメッセージの表示間隔[秒]</Description>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>1540</SortID>
      <UnkB8>Visitor</UnkB8>
      <UnkC0>VISITOR</UnkC0>
      <UnkC8>訪問</UnkC8>
    </Field>
    <Field Def="f32 wanderGhostIntervalLifeTime = 40">
      <DisplayName>徘徊幻影寿命</DisplayName>
      <Description>徘徊幻影寿命</Description>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>550</SortID>
      <UnkB8>ghostAddition</UnkB8>
      <UnkC0>NET_GHOST_ADDITION_PARAM</UnkC0>
      <UnkC8>幻影追加要素</UnkC8>
    </Field>
    <Field Def="dummy8 pad13[12]">
      <DisplayName>予約</DisplayName>
      <Description>予約</Description>
      <SortID>551</SortID>
      <UnkB8>ghostAddition</UnkB8>
      <UnkC0>NET_GHOST_ADDITION_PARAM</UnkC0>
      <UnkC8>幻影追加要素</UnkC8>
    </Field>
    <Field Def="f32 YellowMonkTimeOutTime = 1">
      <DisplayName>黄衣の翁待ちタイムアウト</DisplayName>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>2000</SortID>
      <UnkB8>YellowMonk</UnkB8>
      <UnkC0>YELLOW_MONK_PARAM</UnkC0>
      <UnkC8>黄衣の翁</UnkC8>
    </Field>
    <Field Def="f32 YellowMonkDownloadSpan = 1">
      <DisplayName>黄衣の翁リストダウンロード間隔</DisplayName>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>2010</SortID>
      <UnkB8>YellowMonk</UnkB8>
      <UnkC0>YELLOW_MONK_PARAM</UnkC0>
      <UnkC8>黄衣の翁</UnkC8>
    </Field>
    <Field Def="f32 YellowMonkOverallFlowTimeOutTime = 1">
      <DisplayName>黄衣の翁全体フロータイムアウト</DisplayName>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>2020</SortID>
      <UnkB8>YellowMonk</UnkB8>
      <UnkC0>YELLOW_MONK_PARAM</UnkC0>
      <UnkC8>黄衣の翁</UnkC8>
    </Field>
    <Field Def="dummy8 pad14_0[4]">
      <DisplayName>予約</DisplayName>
      <SortID>2030</SortID>
      <UnkB8>YellowMonk</UnkB8>
      <UnkC0>YELLOW_MONK_PARAM</UnkC0>
      <UnkC8>黄衣の翁</UnkC8>
    </Field>
    <Field Def="dummy8 pad14_1[8]">
      <DisplayName>予約</DisplayName>
      <SortID>2031</SortID>
    </Field>
  </Fields>
</PARAMDEF>