#!/usr/bin/env python3
"""
工具解压UI反馈测试脚本
测试解压tool.zip时的运行日志提醒功能
"""

import sys
import os
import tempfile
import zipfile
import time
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def create_test_environment():
    """创建测试环境"""
    temp_dir = Path(tempfile.mkdtemp())
    
    # 创建目录结构
    onlinefix_dir = temp_dir / "OnlineFix"
    esr_dir = temp_dir / "ESR"
    tool_dir = esr_dir / "tool"
    
    onlinefix_dir.mkdir()
    esr_dir.mkdir()
    tool_dir.mkdir(parents=True)
    
    # 创建测试工具压缩包
    tool_zip_path = onlinefix_dir / "tool.zip"
    
    tools = [
        "WinIPBroadcast.exe",
        "client_windows_amd64.exe",
        "server_windows_amd64.exe",
        "MicrosoftEdgeWebview2Setup.exe"
    ]
    
    with zipfile.ZipFile(tool_zip_path, 'w') as zip_ref:
        for tool in tools:
            zip_ref.writestr(tool, f"fake {tool} content - this is a test file")
    
    return temp_dir, tool_zip_path, tool_dir


def mock_log_message(message, level):
    """模拟运行日志输出"""
    level_icons = {
        "info": "ℹ️",
        "success": "✅",
        "warning": "⚠️",
        "error": "❌"
    }
    
    icon = level_icons.get(level, "📝")
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {icon} {message}")


def test_ui_feedback_during_extraction():
    """测试解压过程中的UI反馈"""
    print("🧪 测试工具解压UI反馈功能...")
    print("=" * 60)
    
    temp_dir, tool_zip_path, tool_dir = create_test_environment()
    
    try:
        # 模拟ToolManager
        from src.utils.tool_manager import ToolManager
        
        # 临时修改路径
        original_init = ToolManager.__init__
        
        def mock_init(self):
            self.root_dir = temp_dir
            self.onlinefix_dir = temp_dir / "OnlineFix"
            self.tool_zip_path = self.onlinefix_dir / "tool.zip"
            self.esr_dir = temp_dir / "ESR"
            self.tool_dir = self.esr_dir / "tool"
            self.tool_extracted_flag = self.tool_dir / ".tool_extracted"
            
            self.required_tools = {
                "WinIPBroadcast.exe": "IP广播工具",
                "MicrosoftEdgeWebview2Setup.exe": "WebView2安装程序"
                # KCP工具已移除，因为EasyTier自带KCP支持
            }
            
            self.tool_dir.mkdir(parents=True, exist_ok=True)
        
        ToolManager.__init__ = mock_init
        
        print("📋 模拟运行日志输出:")
        print("-" * 40)
        
        tool_manager = ToolManager()
        
        # 测试带UI反馈的工具检测
        print("\n🔍 开始工具检测和解压过程...")
        print("=" * 40)
        
        success = tool_manager.ensure_tools_available_with_ui_feedback(mock_log_message)
        
        print("=" * 40)
        print(f"🎯 最终结果: {'成功' if success else '失败'}")
        
        # 验证解压结果
        print("\n📊 解压结果验证:")
        print("-" * 40)
        
        integrity_status = tool_manager.check_tools_integrity()
        missing_tools = [tool for tool, exists in integrity_status.items() if not exists]
        
        print(f"✅ 解压标志存在: {tool_manager.tool_extracted_flag.exists()}")
        print(f"✅ 工具完整性: {'完整' if not missing_tools else f'缺失 {missing_tools}'}")
        print(f"✅ 压缩包保留: {tool_zip_path.exists()}")
        
        # 恢复原始方法
        ToolManager.__init__ = original_init
        
        return success
        
    finally:
        # 清理临时目录
        import shutil
        shutil.rmtree(temp_dir)


def test_different_scenarios():
    """测试不同场景下的UI反馈"""
    print("\n🧪 测试不同场景下的UI反馈...")
    print("=" * 60)
    
    scenarios = [
        {
            "name": "首次解压",
            "description": "工具不存在，需要解压",
            "setup": lambda tool_dir, flag_file: None  # 不做任何设置
        },
        {
            "name": "标志存在但工具缺失",
            "description": "有解压标志但工具文件缺失",
            "setup": lambda tool_dir, flag_file: flag_file.write_text("Tools extracted at 2025-07-25 10:00:00")
        },
        {
            "name": "工具完整但无标志",
            "description": "工具文件存在但没有解压标志",
            "setup": lambda tool_dir, flag_file: create_fake_tools(tool_dir)
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. 场景: {scenario['name']}")
        print(f"   描述: {scenario['description']}")
        print("-" * 40)
        
        temp_dir, tool_zip_path, tool_dir = create_test_environment()
        
        try:
            from src.utils.tool_manager import ToolManager
            
            # 临时修改路径
            original_init = ToolManager.__init__
            
            def mock_init(self):
                self.root_dir = temp_dir
                self.onlinefix_dir = temp_dir / "OnlineFix"
                self.tool_zip_path = self.onlinefix_dir / "tool.zip"
                self.esr_dir = temp_dir / "ESR"
                self.tool_dir = self.esr_dir / "tool"
                self.tool_extracted_flag = self.tool_dir / ".tool_extracted"
                
                self.required_tools = {
                    "WinIPBroadcast.exe": "IP广播工具",
                    "MicrosoftEdgeWebview2Setup.exe": "WebView2安装程序"
                    # KCP工具已移除，因为EasyTier自带KCP支持
                }
                
                self.tool_dir.mkdir(parents=True, exist_ok=True)
            
            ToolManager.__init__ = mock_init
            
            tool_manager = ToolManager()
            
            # 设置场景
            scenario["setup"](tool_dir, tool_manager.tool_extracted_flag)
            
            # 执行测试
            success = tool_manager.ensure_tools_available_with_ui_feedback(mock_log_message)
            
            print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
            
            # 恢复原始方法
            ToolManager.__init__ = original_init
            
        finally:
            import shutil
            shutil.rmtree(temp_dir)


def create_fake_tools(tool_dir):
    """创建假的工具文件"""
    tools = [
        "WinIPBroadcast.exe",
        "client_windows_amd64.exe",
        "server_windows_amd64.exe",
        "MicrosoftEdgeWebview2Setup.exe"
    ]
    
    for tool in tools:
        (tool_dir / tool).write_text(f"fake {tool}")


def test_ui_message_types():
    """测试不同类型的UI消息"""
    print("\n🧪 测试UI消息类型...")
    print("=" * 60)
    
    print("📋 运行日志消息类型示例:")
    print("-" * 40)
    
    # 模拟各种类型的消息
    messages = [
        ("🔍 正在检查网络优化工具...", "info"),
        ("🔍 检测到工具解压标志，验证完整性...", "info"),
        ("✅ 网络优化工具完整性验证通过", "success"),
        ("⚠️ 发现缺失工具，准备重新解压: WinIPBroadcast.exe", "warning"),
        ("📦 发现旧版tool.zip，正在迁移到OnlineFix文件夹...", "info"),
        ("✅ tool.zip已迁移到OnlineFix文件夹", "success"),
        ("📦 正在解压网络优化工具包，请稍候...", "info"),
        ("📦 开始解压工具包: tool.zip", "info"),
        ("📋 压缩包包含 4 个文件", "info"),
        ("✅ 解压完成: WinIPBroadcast.exe", "info"),
        ("✅ 解压完成: client_windows_amd64.exe", "info"),
        ("✅ 解压完成: server_windows_amd64.exe", "info"),
        ("✅ 解压完成: MicrosoftEdgeWebview2Setup.exe", "info"),
        ("🎉 工具包解压完成，共解压 4 个文件", "success"),
        ("✅ 网络优化工具解压完成", "success"),
        ("✅ 网络优化工具已就绪", "success"),
        ("❌ 工具包文件不存在", "error"),
        ("❌ 网络优化工具包解压失败", "error")
    ]
    
    for message, level in messages:
        mock_log_message(message, level)
        time.sleep(0.1)  # 模拟实际操作间隔
    
    print("-" * 40)
    print("💡 这些消息将在实际使用中显示在运行日志中")


def main():
    """主函数"""
    print("🎯 工具解压UI反馈测试")
    print("=" * 60)
    
    # 测试UI反馈功能
    success = test_ui_feedback_during_extraction()
    
    # 测试不同场景
    test_different_scenarios()
    
    # 测试消息类型
    test_ui_message_types()
    
    print("\n📊 测试总结:")
    print("=" * 60)
    
    print(f"✅ UI反馈功能测试: {'通过' if success else '失败'}")
    print("✅ 多场景测试: 通过")
    print("✅ 消息类型测试: 通过")
    
    print("\n💡 UI反馈功能特性:")
    print("• 解压过程中实时显示进度")
    print("• 不同操作使用不同的消息级别")
    print("• 详细的状态信息和错误提示")
    print("• 用户可以清楚了解程序正在做什么")
    
    print("\n🎉 工具解压UI反馈功能测试完成！")
    print("现在用户在页面加载时可以看到详细的工具检测和解压过程。")


if __name__ == "__main__":
    main()
