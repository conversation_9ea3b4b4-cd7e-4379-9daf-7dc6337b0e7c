#!/usr/bin/env python3
"""
语言切换功能调试版本
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PySide6.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
                               QPushButton, QLabel, QMenu)
from PySide6.QtCore import Qt

# 直接导入语言管理器
from src.utils.language_manager import language_manager, tr


class DebugLanguageWindow(QWidget):
    """调试语言切换窗口"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
        # 连接语言切换信号
        language_manager.language_changed.connect(self.on_language_changed)
        print("✅ 语言切换信号已连接")
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("语言切换调试")
        self.setFixedSize(500, 400)
        
        layout = QVBoxLayout()
        
        # 标题
        self.title_label = QLabel()
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("font-size: 20px; font-weight: bold; margin: 20px;")
        
        # 当前语言显示
        self.current_lang_label = QLabel()
        self.current_lang_label.setAlignment(Qt.AlignCenter)
        self.current_lang_label.setStyleSheet("font-size: 14px; margin: 10px;")
        
        # 语言切换按钮（模拟标题栏按钮）
        self.language_btn = self.create_language_button()
        
        # 测试文本标签
        self.test_labels = []
        test_keys = [
            ("virtual_lan_title", "虚拟局域网"),
            ("room_name", "房间名称"),
            ("room_password", "房间密码"),
            ("start_network", "启动网络"),
            ("stop_network", "停止网络"),
            ("share_room", "分享房间"),
            ("join_room", "加入房间"),
        ]
        
        for key, default in test_keys:
            label = QLabel()
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("margin: 5px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;")
            self.test_labels.append((label, key, default))
            layout.addWidget(label)
        
        # 直接切换按钮（用于测试）
        btn_layout = QHBoxLayout()
        
        direct_chinese_btn = QPushButton("直接切换到中文")
        direct_chinese_btn.clicked.connect(lambda: self.direct_switch_language("zh_CN"))
        
        direct_english_btn = QPushButton("Direct Switch to English")
        direct_english_btn.clicked.connect(lambda: self.direct_switch_language("en_US"))
        
        btn_layout.addWidget(direct_chinese_btn)
        btn_layout.addWidget(direct_english_btn)
        
        # 添加到主布局
        layout.addWidget(self.title_label)
        layout.addWidget(self.current_lang_label)
        layout.addWidget(self.language_btn)
        layout.addStretch()
        layout.addLayout(btn_layout)
        
        self.setLayout(layout)
        
        # 初始化文本
        self.update_all_text()
    
    def create_language_button(self):
        """创建语言切换按钮（模拟标题栏）"""
        language_btn = QPushButton("🌐 语言")
        language_btn.setFixedSize(100, 35)
        language_btn.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                font-size: 14px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
        
        # 创建语言菜单
        language_menu = QMenu()
        language_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 6px;
                padding: 4px;
            }
            QMenu::item {
                background-color: transparent;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #e0e0e0;
            }
        """)
        
        # 添加语言选项
        chinese_action = language_menu.addAction("🇨🇳 中文")
        english_action = language_menu.addAction("🇺🇸 English")
        
        # 连接信号
        chinese_action.triggered.connect(lambda: self.switch_language("zh_CN"))
        english_action.triggered.connect(lambda: self.switch_language("en_US"))
        
        # 直接连接点击事件来显示菜单
        def show_menu():
            language_menu.exec(language_btn.mapToGlobal(language_btn.rect().bottomLeft()))

        language_btn.clicked.connect(show_menu)
        return language_btn
    
    def switch_language(self, language_code: str):
        """通过菜单切换语言"""
        print(f"🔄 菜单切换语言: {language_code}")
        try:
            if language_manager.set_language(language_code):
                print(f"✅ 语言切换成功: {language_code}")
            else:
                print(f"❌ 语言切换失败: {language_code}")
        except Exception as e:
            print(f"❌ 语言切换错误: {e}")
            import traceback
            traceback.print_exc()
    
    def direct_switch_language(self, language_code: str):
        """直接切换语言（用于测试）"""
        print(f"🔄 直接切换语言: {language_code}")
        try:
            if language_manager.set_language(language_code):
                print(f"✅ 直接切换成功: {language_code}")
            else:
                print(f"❌ 直接切换失败: {language_code}")
        except Exception as e:
            print(f"❌ 直接切换错误: {e}")
            import traceback
            traceback.print_exc()
    
    def on_language_changed(self, language_code: str):
        """语言切换回调"""
        print(f"🔔 收到语言切换信号: {language_code}")
        self.update_all_text()
    
    def update_all_text(self):
        """更新所有文本"""
        try:
            current_lang = language_manager.get_current_language()
            print(f"🔄 更新界面文本，当前语言: {current_lang}")
            
            # 更新标题
            title = tr("app_title", "Nmodm - 艾尔登法环联机工具")
            self.title_label.setText(title)
            
            # 更新当前语言显示
            lang_name = tr("language_chinese" if current_lang == "zh_CN" else "language_english")
            self.current_lang_label.setText(f"当前语言 / Current Language: {lang_name}")
            
            # 更新测试文本
            for label, key, default in self.test_labels:
                text = tr(key, default)
                label.setText(f"{key}: {text}")
            
            print(f"✅ 界面文本更新完成")
            
        except Exception as e:
            print(f"❌ 更新文本失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("Language Debug")
    app.setApplicationVersion("1.0.0")
    
    # 打印初始状态
    print("🚀 启动语言切换调试程序")
    print(f"初始语言: {language_manager.get_current_language()}")
    print(f"可用语言: {language_manager.get_available_languages()}")
    
    # 创建调试窗口
    window = DebugLanguageWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
