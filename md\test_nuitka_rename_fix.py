#!/usr/bin/env python3
"""
测试 Nuitka 打包脚本的重命名修复
验证新的权限处理和重命名逻辑
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def test_nuitka_rename_methods():
    """测试 Nuitka 重命名方法"""
    print("🧪 测试 Nuitka 重命名方法修复...")
    print("=" * 60)
    
    # 导入修复后的 NuitkaBuilder
    from build_nuitka import NuitkaBuilder
    
    builder = NuitkaBuilder()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        print("1. 测试强制删除目录方法...")
        
        # 创建测试目录
        test_dir = temp_path / "test_delete"
        test_dir.mkdir()
        
        # 创建一些测试文件
        (test_dir / "test_file.txt").write_text("test content")
        (test_dir / "subdir").mkdir()
        (test_dir / "subdir" / "nested_file.txt").write_text("nested content")
        
        print(f"   📁 创建测试目录: {test_dir}")
        print(f"   📄 目录内容: {list(test_dir.rglob('*'))}")
        
        # 测试删除
        result = builder.force_remove_directory(test_dir)
        
        if result and not test_dir.exists():
            print("   ✅ 强制删除目录测试通过")
        else:
            print("   ❌ 强制删除目录测试失败")
        
        print()
        
        print("2. 测试安全重命名目录方法...")
        
        # 创建源目录
        source_dir = temp_path / "source_dir"
        source_dir.mkdir()
        (source_dir / "app.exe").write_text("fake exe content")
        (source_dir / "data.txt").write_text("data content")
        
        target_dir = temp_path / "target_dir"
        
        print(f"   📁 源目录: {source_dir}")
        print(f"   📁 目标目录: {target_dir}")
        
        # 测试重命名
        result = builder.safe_rename_directory(source_dir, target_dir)
        
        if result and target_dir.exists() and not source_dir.exists():
            print("   ✅ 安全重命名目录测试通过")
            print(f"   📄 重命名后内容: {list(target_dir.rglob('*'))}")
        else:
            print("   ❌ 安全重命名目录测试失败")
        
        print()
        
        print("3. 测试重命名冲突处理...")
        
        # 创建新的源目录
        source_dir2 = temp_path / "source_dir2"
        source_dir2.mkdir()
        (source_dir2 / "new_app.exe").write_text("new fake exe content")
        
        # 目标目录已存在（从上一个测试）
        print(f"   📁 源目录: {source_dir2}")
        print(f"   📁 目标目录: {target_dir} (已存在)")
        
        # 先删除已存在的目标目录
        delete_result = builder.force_remove_directory(target_dir)
        if delete_result:
            print("   ✅ 成功删除已存在的目标目录")
            
            # 再次测试重命名
            rename_result = builder.safe_rename_directory(source_dir2, target_dir)
            if rename_result and target_dir.exists():
                print("   ✅ 冲突处理后重命名测试通过")
            else:
                print("   ❌ 冲突处理后重命名测试失败")
        else:
            print("   ❌ 删除已存在目录失败")
        
        print()
    
    print("🎉 Nuitka 重命名方法测试完成！")


def test_nuitka_script_structure():
    """测试 Nuitka 脚本结构"""
    print("🧪 测试 Nuitka 脚本结构...")
    print("=" * 60)
    
    try:
        from build_nuitka import NuitkaBuilder
        
        builder = NuitkaBuilder()
        
        # 检查新方法是否存在
        methods_to_check = [
            'force_remove_directory',
            'safe_rename_directory'
        ]
        
        print("📋 检查新增方法:")
        for method_name in methods_to_check:
            if hasattr(builder, method_name):
                method = getattr(builder, method_name)
                if callable(method):
                    print(f"   ✅ {method_name}: 存在且可调用")
                else:
                    print(f"   ❌ {method_name}: 存在但不可调用")
            else:
                print(f"   ❌ {method_name}: 不存在")
        
        print()
        
        # 检查版本号
        print(f"📋 版本信息:")
        print(f"   版本号: {builder.version}")
        print(f"   项目根目录: {builder.project_root}")
        print(f"   构建目录: {builder.dist_dir}")
        
        print()
        
        print("✅ Nuitka 脚本结构检查完成")
        
    except Exception as e:
        print(f"❌ Nuitka 脚本结构检查失败: {e}")


def show_fix_summary():
    """显示修复总结"""
    print("📋 Nuitka 打包脚本修复总结")
    print("=" * 60)
    
    print("🔧 修复内容:")
    print("1. ✅ 添加了 force_remove_directory() 方法")
    print("   • 强制删除目录，支持多次重试")
    print("   • 自动修改文件权限")
    print("   • 处理权限错误和文件占用")
    print()
    
    print("2. ✅ 添加了 safe_rename_directory() 方法")
    print("   • 安全重命名目录，支持多次重试")
    print("   • 自动终止占用进程")
    print("   • 备用复制+删除方案")
    print("   • 详细的错误处理和用户提示")
    print()
    
    print("3. ✅ 改进了重命名逻辑")
    print("   • 使用新的强化方法替代简单的 rename()")
    print("   • 更好的错误处理和用户反馈")
    print("   • 减少因权限问题导致的打包失败")
    print()
    
    print("🎯 预期效果:")
    print("• 解决 [WinError 5] 拒绝访问 的重命名错误")
    print("• 提高打包成功率")
    print("• 更好的用户体验和错误提示")
    print("• 自动处理常见的权限和占用问题")
    print()
    
    print("💡 使用建议:")
    print("• 如果仍然遇到权限问题，请以管理员身份运行")
    print("• 确保没有其他程序正在使用构建目录")
    print("• 关闭可能占用文件的杀毒软件或文件管理器")


if __name__ == "__main__":
    show_fix_summary()
    test_nuitka_script_structure()
    test_nuitka_rename_methods()
