﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>MAP_GD_REGION_DRAW_PARAM</ParamType>
  <DataVersion>2</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 disableParam_NT:1">
      <DisplayName>NT版出力から外すか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータをNT版パッケージでは除外します</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
    </Field>
    <Field Def="dummy8 disableParamReserve1:7">
      <DisplayName>パッケージ出力用リザーブ1</DisplayName>
      <Description>パッケージ出力用リザーブ1</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>5</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve2[3]">
      <DisplayName>パッケージ出力用リザーブ2</DisplayName>
      <Description>パッケージ出力用リザーブ2</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>6</SortID>
    </Field>
    <Field Def="f32 overrideIVLocalLightScale = -1">
      <DisplayName>IV撮影時のローカルライトスケール上書き値</DisplayName>
      <Description>IV撮影時のローカルライトスケール値(0以下：上書きなし) 【GR】SEQ13338 【イラディアンスボリューム】SFX光源のインダイレクトスケールを一律に変更したい</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>256</Maximum>
      <Increment>0.1</Increment>
      <SortID>1</SortID>
    </Field>
  </Fields>
</PARAMDEF>