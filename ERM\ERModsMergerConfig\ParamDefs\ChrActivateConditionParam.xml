﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>CHR_ACTIVATE_CONDITION_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 weatherSunny:1 = 1">
      <DisplayName>出現条件_晴れ</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「晴れ」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="u8 weatherClearSky:1 = 1">
      <DisplayName>出現条件_快晴</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「快晴」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>1100</SortID>
    </Field>
    <Field Def="u8 weatherWeakCloudy:1 = 1">
      <DisplayName>出現条件_薄曇り</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「薄曇り」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="u8 weatherCloudy:1 = 1">
      <DisplayName>出現条件_曇り</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「曇り」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="u8 weatherRain:1 = 1">
      <DisplayName>出現条件_雨</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「雨」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>3000</SortID>
    </Field>
    <Field Def="u8 weatherHeavyRain:1 = 1">
      <DisplayName>出現条件_豪雨</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「豪雨」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>3100</SortID>
    </Field>
    <Field Def="u8 weatherStorm:1 = 1">
      <DisplayName>出現条件_嵐</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「嵐」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>4000</SortID>
    </Field>
    <Field Def="u8 weatherStormForBattle:1 = 1">
      <DisplayName>出現条件_嵐（守護者の末裔との戦闘用）</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「嵐（守護者の末裔との戦闘用）」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>4100</SortID>
    </Field>
    <Field Def="u8 weatherSnow:1 = 1">
      <DisplayName>出現条件_雪</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「雪」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>5000</SortID>
    </Field>
    <Field Def="u8 weatherHeavySnow:1 = 1">
      <DisplayName>出現条件_大雪</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「大雪」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>5100</SortID>
    </Field>
    <Field Def="u8 weatherFog:1 = 1">
      <DisplayName>出現条件_霧</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「霧」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>6000</SortID>
    </Field>
    <Field Def="u8 weatherHeavyFog:1 = 1">
      <DisplayName>出現条件_濃霧</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「濃霧」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>6100</SortID>
    </Field>
    <Field Def="u8 weatherHeavyFogRain:1 = 1">
      <DisplayName>出現条件_濃霧（雨）</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「濃霧（雨）」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>6200</SortID>
    </Field>
    <Field Def="u8 weatherSandStorm:1 = 1">
      <DisplayName>出現条件_砂嵐</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>天候が「砂嵐」のときに出現するか</Description>
      <Maximum>1</Maximum>
      <SortID>7000</SortID>
    </Field>
    <Field Def="dummy8 pad1:2">
      <DisplayName>pad</DisplayName>
      <SortID>8301</SortID>
    </Field>
    <Field Def="u8 timeStartHour">
      <DisplayName>出現開始インゲーム時間_時</DisplayName>
      <Description>出現開始インゲーム時間_時</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>23</Maximum>
      <SortID>8000</SortID>
    </Field>
    <Field Def="u8 timeStartMin">
      <DisplayName>出現開始インゲーム時間_分</DisplayName>
      <Description>出現開始インゲーム時間_分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>59</Maximum>
      <SortID>8100</SortID>
    </Field>
    <Field Def="u8 timeEndHour">
      <DisplayName>出現終了インゲーム時間_時</DisplayName>
      <Description>出現終了インゲーム時間_時</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>23</Maximum>
      <SortID>8200</SortID>
    </Field>
    <Field Def="u8 timeEndMin">
      <DisplayName>出現終了インゲーム時間_分</DisplayName>
      <Description>出現終了インゲーム時間_分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>59</Maximum>
      <SortID>8300</SortID>
    </Field>
    <Field Def="dummy8 pad2[2]">
      <DisplayName>pad</DisplayName>
      <SortID>8302</SortID>
    </Field>
  </Fields>
</PARAMDEF>