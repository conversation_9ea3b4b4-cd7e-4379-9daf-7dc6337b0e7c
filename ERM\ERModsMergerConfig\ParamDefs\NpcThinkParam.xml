﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>NPC_THINK_PARAM_ST</ParamType>
  <DataVersion>2</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 disableParam_NT:1">
      <DisplayName>NT版出力から外すか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータをNT版パッケージでは除外します</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
    </Field>
    <Field Def="dummy8 disableParamReserve1:7">
      <DisplayName>パッケージ出力用リザーブ1</DisplayName>
      <Description>パッケージ出力用リザーブ1</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>15201</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve2[3]">
      <DisplayName>パッケージ出力用リザーブ2</DisplayName>
      <Description>パッケージ出力用リザーブ2</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>15202</SortID>
    </Field>
    <Field Def="s32 logicId = -1">
      <DisplayName>ロジックスクリプトID</DisplayName>
      <Description>スクリプトで作成したロジックのIDを設定します。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="s32 battleGoalID = -1">
      <DisplayName>戦闘ゴールID</DisplayName>
      <Description>戦闘ゴールID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="u16 searchEye_dist">
      <DisplayName>索敵視覚_距離[m]</DisplayName>
      <Description>索敵視覚による索敵範囲.</Description>
      <SortID>11310</SortID>
    </Field>
    <Field Def="u8 searchEye_angY">
      <DisplayName>索敵視覚_角度（幅）[deg]</DisplayName>
      <Description>索敵視覚による索敵範囲.</Description>
      <Maximum>180</Maximum>
      <SortID>11330</SortID>
    </Field>
    <Field Def="u8 isNoAvoidHugeEnemy:1">
      <DisplayName>巨大敵を迂回しないか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>巨大敵を迂回しないか</Description>
      <Maximum>1</Maximum>
      <SortID>13560</SortID>
    </Field>
    <Field Def="u8 enableWeaponOnOff:1">
      <DisplayName>納刀抜刀するか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>納刀抜刀するか</Description>
      <Maximum>1</Maximum>
      <SortID>13010</SortID>
    </Field>
    <Field Def="u8 targetAILockDmyPoly:1 = 1">
      <DisplayName>ロックダミポリ(エネミー用)を狙うか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>ロックダミポリ(エネミー用)を狙うか</Description>
      <Maximum>1</Maximum>
      <SortID>10650</SortID>
    </Field>
    <Field Def="dummy8 pad8:5">
      <DisplayName>パディング</DisplayName>
      <DisplayFormat>%f</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>15203</SortID>
    </Field>
    <Field Def="s32 spEffectId_RangedAttack = -1">
      <DisplayName>遠隔攻撃用特殊効果ID</DisplayName>
      <Description>遠隔攻撃用特殊効果ID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>10330</SortID>
    </Field>
    <Field Def="f32 searchTargetLv1ForgetTime = 5">
      <DisplayName>索敵Lv1ターゲット忘れる時間[sec]</DisplayName>
      <Description>索敵Lv1ターゲット忘れる時間。</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>12510</SortID>
    </Field>
    <Field Def="f32 searchTargetLv2ForgetTime = 8">
      <DisplayName>索敵Lv2ターゲット忘れる時間[sec]</DisplayName>
      <Description>索敵Lv2ターゲット忘れる時間。</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>12511</SortID>
    </Field>
    <Field Def="f32 BackHomeLife_OnHitEneWal = 5">
      <DisplayName>敵壁接触時のBackHome時間[sec]</DisplayName>
      <Description>ブロックをさえぎる敵壁に接触したとき、BackToHomeゴールの寿命</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>10600</SortID>
    </Field>
    <Field Def="f32 SightTargetForgetTime = 600">
      <DisplayName>視覚ターゲット忘れる時間[sec]</DisplayName>
      <Description>視覚ターゲット忘れる時間。</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>12300</SortID>
    </Field>
    <Field Def="s32 idAttackCannotMove">
      <DisplayName>動けなくなったときに行うEzState番号</DisplayName>
      <Description>破壊可能なオブジェクトによって動きが止まっている場合、自動的に実行する行動。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>10500</SortID>
    </Field>
    <Field Def="f32 ear_dist">
      <DisplayName>聴覚_距離[m]</DisplayName>
      <Description>聴覚による索敵範囲.。</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>11400</SortID>
    </Field>
    <Field Def="s32 callHelp_ActionAnimId = -1">
      <DisplayName>仲間呼び 応答アクションアニメID</DisplayName>
      <Description>応答する時のアニメID(EzStateAnimID)</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>14500</SortID>
    </Field>
    <Field Def="s32 callHelp_CallActionId = -1">
      <DisplayName>仲間呼び_仲間呼びアクションID</DisplayName>
      <Description>仲間呼ぶときのアクションID(EzStateAnimID)</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>14600</SortID>
    </Field>
    <Field Def="u16 eye_dist">
      <DisplayName>視覚_距離[m]</DisplayName>
      <Description>視覚による索敵範囲.</Description>
      <SortID>10800</SortID>
    </Field>
    <Field Def="u8 isGuard_Act">
      <DisplayName>行動時ガード使用するか</DisplayName>
      <Enum>BOOL_DODONT_TYPE</Enum>
      <Description>行動にガードするか（帰宅時、ターゲットの方を見ている時想定）</Description>
      <Maximum>1</Maximum>
      <SortID>10310</SortID>
    </Field>
    <Field Def="dummy8 pad6[1]">
      <DisplayName>パディング</DisplayName>
      <DisplayFormat>%f</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>15204</SortID>
    </Field>
    <Field Def="u16 ear_soundcut_dist">
      <DisplayName>聴覚　影響カット距離[m]</DisplayName>
      <Description>音源のサイズを小さくする距離。この距離未満の音が聞こえなくなります。</Description>
      <SortID>11500</SortID>
    </Field>
    <Field Def="u16 nose_dist">
      <DisplayName>嗅覚_距離[m]</DisplayName>
      <Description>嗅覚による索敵範囲.</Description>
      <SortID>11600</SortID>
    </Field>
    <Field Def="u16 maxBackhomeDist">
      <DisplayName>何があっても帰宅する距離[m]</DisplayName>
      <Description>COMMON_SetBattleActLogicの引き数</Description>
      <SortID>11700</SortID>
    </Field>
    <Field Def="u16 backhomeDist">
      <DisplayName>戦闘しつつ帰宅する距離[m]</DisplayName>
      <Description>COMMON_SetBattleActLogicの引き数</Description>
      <SortID>11800</SortID>
    </Field>
    <Field Def="u16 backhomeBattleDist">
      <DisplayName>巣に帰るのをあきらめて戦闘する距離[m]</DisplayName>
      <Description>COMMON_SetBattleActLogicの引き数</Description>
      <SortID>11900</SortID>
    </Field>
    <Field Def="u16 nonBattleActLife">
      <DisplayName>敵を意識しているときの非戦闘行動時間[sec]</DisplayName>
      <Description>COMMON_SetBattleActLogicの引き数</Description>
      <Maximum>65534</Maximum>
      <SortID>12000</SortID>
    </Field>
    <Field Def="u16 BackHome_LookTargetTime = 3">
      <DisplayName>帰宅時：ターゲットを見ている時間[sec]</DisplayName>
      <Description>帰宅時：ターゲットを見ている時間[sec]</Description>
      <Maximum>65534</Maximum>
      <SortID>12100</SortID>
    </Field>
    <Field Def="u16 BackHome_LookTargetDist = 10">
      <DisplayName>帰宅時：ターゲットを見ている距離[m]</DisplayName>
      <Description>帰宅時：ターゲットを見ている距離[m]</Description>
      <Maximum>65534</Maximum>
      <SortID>12200</SortID>
    </Field>
    <Field Def="f32 SoundTargetForgetTime = 3">
      <DisplayName>音ターゲット忘れる時間[sec]</DisplayName>
      <Description>音ターゲット忘れる時間。</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>12400</SortID>
    </Field>
    <Field Def="u16 BattleStartDist">
      <DisplayName>戦闘開始距離[m]</DisplayName>
      <Maximum>65534</Maximum>
      <SortID>10900</SortID>
    </Field>
    <Field Def="u16 callHelp_MyPeerId">
      <DisplayName>仲間呼び 自分の仲間グループID</DisplayName>
      <Description>自分の仲間グループID</Description>
      <Maximum>65534</Maximum>
      <SortID>14300</SortID>
    </Field>
    <Field Def="u16 callHelp_CallPeerId">
      <DisplayName>仲間呼び 呼ぶ仲間グループID</DisplayName>
      <Description>仲間を呼ぶ対象となる仲間グループID</Description>
      <Maximum>65534</Maximum>
      <SortID>14400</SortID>
    </Field>
    <Field Def="u16 targetSys_DmgEffectRate = 100">
      <DisplayName>ダメージ影響率[％]</DisplayName>
      <Description>ダメージ影響率取得(ターゲットシステム評価情報)</Description>
      <Maximum>1000</Maximum>
      <SortID>13100</SortID>
    </Field>
    <Field Def="u8 TeamAttackEffectivity = 25">
      <DisplayName>チーム攻撃影響力[0-100]</DisplayName>
      <Description>チーム内の同時攻撃人数を決めるための値。値を大きくすると、同時に攻撃参加できる人数が少なくなる。</Description>
      <Maximum>100</Maximum>
      <SortID>13200</SortID>
    </Field>
    <Field Def="u8 eye_angX">
      <DisplayName>視覚_角度（高さ）[deg]</DisplayName>
      <Description>視覚による索敵範囲.</Description>
      <Maximum>180</Maximum>
      <SortID>11000</SortID>
    </Field>
    <Field Def="u8 eye_angY">
      <DisplayName>視覚_角度（幅）[deg]</DisplayName>
      <Description>視覚による索敵範囲.</Description>
      <Maximum>180</Maximum>
      <SortID>11100</SortID>
    </Field>
    <Field Def="u8 disableDark">
      <DisplayName>暗闇影響しない</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>警戒視覚_距離、戦闘開始距離が暗闇による影響を受けないようにするか</Description>
      <Maximum>1</Maximum>
      <SortID>10700</SortID>
    </Field>
    <Field Def="u8 caravanRole">
      <DisplayName>キャラバンでの役割</DisplayName>
      <Enum>NPC_THINK_CARAVAN_ROLE</Enum>
      <Description>キャラバンでの役割</Description>
      <Maximum>3</Maximum>
      <SortID>13510</SortID>
    </Field>
    <Field Def="u8 callHelp_CallValidMinDistTarget = 5">
      <DisplayName>仲間呼び_ターゲットとの最低距離[m]</DisplayName>
      <Description>この値より近い場合は仲間呼びできない.</Description>
      <SortID>15000</SortID>
    </Field>
    <Field Def="u8 callHelp_CallValidRange = 15">
      <DisplayName>仲間呼び_仲間を呼ぶ有効距離[m]</DisplayName>
      <Description>この値より仲間が遠い場合は呼ばない。</Description>
      <SortID>14900</SortID>
    </Field>
    <Field Def="u8 callHelp_ForgetTimeByArrival">
      <DisplayName>仲間呼び 応答してから忘れる時間[sec]</DisplayName>
      <Description>応答する時間</Description>
      <SortID>14800</SortID>
    </Field>
    <Field Def="u8 callHelp_MinWaitTime">
      <DisplayName>応答時の待機最小時間[ssm=&gt;ss．mSec]</DisplayName>
      <Description>応答ゴールの最初の待機ゴールでの最小時間[101=&gt;10．1sec]</Description>
      <SortID>15100</SortID>
    </Field>
    <Field Def="u8 callHelp_MaxWaitTime">
      <DisplayName>応答時の待機最大時間[ssm=&gt;ss．mSec]</DisplayName>
      <Description>応答ゴールの最初の待機ゴールでの最大時間[101=&gt;10．1sec]</Description>
      <SortID>15200</SortID>
    </Field>
    <Field Def="u8 goalAction_ToCaution">
      <DisplayName>ゴールアクション：警戒状態/通常音</DisplayName>
      <Enum>NPC_THINK_GOAL_ACTION</Enum>
      <Description>ゴールアクション：ターゲットが通常音の感知により警戒状態になった</Description>
      <SortID>12600</SortID>
    </Field>
    <Field Def="u8 ear_listenLevel = 128">
      <DisplayName>聴覚_可聴AI音レベル</DisplayName>
      <Description>どれくらい耳が良いのか</Description>
      <Maximum>128</Maximum>
      <SortID>11550</SortID>
    </Field>
    <Field Def="u8 callHelp_ReplyBehaviorType">
      <DisplayName>仲間呼び 応答後の行動タイプ</DisplayName>
      <Enum>NPC_THINK_REPLY_BEHAVIOR_TYPE</Enum>
      <Description>応答後、目標位置までの行動タイプ</Description>
      <Maximum>3</Maximum>
      <SortID>14700</SortID>
    </Field>
    <Field Def="u8 disablePathMove">
      <DisplayName>パス移動しない</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>パス移動命令が来てもパスを辿らずに直接移動するか</Description>
      <Maximum>1</Maximum>
      <SortID>10100</SortID>
    </Field>
    <Field Def="u8 skipArrivalVisibleCheck">
      <DisplayName>視線による到着判定をスキップするか？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>視線による到着判定をスキップするか？Onにすると、視線が通っていなくても、到着判定を行う。</Description>
      <Maximum>1</Maximum>
      <SortID>10400</SortID>
    </Field>
    <Field Def="u8 thinkAttr_doAdmirer">
      <DisplayName>取巻き役になるか？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>思考属性：ＯＮにすると取巻き役を演じます。</Description>
      <Maximum>1</Maximum>
      <SortID>13300</SortID>
    </Field>
    <Field Def="u8 enableNaviFlg_Edge:1 = 1">
      <DisplayName>フラグ「崖」通れるか？</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>ノード「崖」を通過できるか？(def:1)</Description>
      <Maximum>1</Maximum>
      <SortID>13900</SortID>
    </Field>
    <Field Def="u8 enableNaviFlg_LargeSpace:1 = 1">
      <DisplayName>フラグ「広い」通れるか？</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>ノード「広い」を通過できるか？(def:1)</Description>
      <Maximum>1</Maximum>
      <SortID>14000</SortID>
    </Field>
    <Field Def="u8 enableNaviFlg_Ladder:1">
      <DisplayName>フラグ「梯子」通れるか？</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>ノード「梯子」を通過できるか？(def:0)</Description>
      <Maximum>1</Maximum>
      <SortID>13600</SortID>
    </Field>
    <Field Def="u8 enableNaviFlg_Hole:1">
      <DisplayName>フラグ「穴」通れるか？</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>ノード「穴」を通過できるか？(def:0)</Description>
      <Maximum>1</Maximum>
      <SortID>14100</SortID>
    </Field>
    <Field Def="u8 enableNaviFlg_Door:1">
      <DisplayName>フラグ「扉」通れるか？</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>ノード「扉」を通過できるか？(def:0)</Description>
      <Maximum>1</Maximum>
      <SortID>13700</SortID>
    </Field>
    <Field Def="u8 enableNaviFlg_InSideWall:1">
      <DisplayName>フラグ「壁中」通れるか？</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>ノード「壁中」を通過できるか？(def:0)</Description>
      <Maximum>1</Maximum>
      <SortID>14200</SortID>
    </Field>
    <Field Def="u8 enableNaviFlg_Lava:1">
      <DisplayName>フラグ「溶岩」通れるか？</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>ノード「溶岩」を通過できるか？(def:0)</Description>
      <Maximum>1</Maximum>
      <SortID>13710</SortID>
    </Field>
    <Field Def="u8 enableNaviFlg_Edge_Ordinary:1 = 1">
      <DisplayName>フラグ「崖」通れるか？（通常／警戒状態）</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>通常／警戒状態でノード「崖」を通過できるか？(def:1)</Description>
      <Maximum>1</Maximum>
      <SortID>13800</SortID>
    </Field>
    <Field Def="dummy8 enableNaviFlg_reserve1[3]">
      <DisplayName>ほんとに予約</DisplayName>
      <Description>フラグが新しく必要になったらここにいれます（NotPadding)</Description>
      <SortID>15205</SortID>
    </Field>
    <Field Def="s32 searchThreshold_Lv0toLv1 = 10">
      <DisplayName>索敵Lv0→Lv1の閾値</DisplayName>
      <Description>索敵Lv0→Lv1の閾値</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>12520</SortID>
    </Field>
    <Field Def="s32 searchThreshold_Lv1toLv2 = 70">
      <DisplayName>索敵Lv1→Lv2の閾値</DisplayName>
      <Description>索敵Lv1→Lv2の閾値</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>12521</SortID>
    </Field>
    <Field Def="f32 platoonReplyTime">
      <DisplayName>小隊反応遅延時間[sec]</DisplayName>
      <Description>小隊反応遅延時間[sec]</Description>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>0.1</Increment>
      <SortID>13400</SortID>
    </Field>
    <Field Def="f32 platoonReplyAddRandomTime">
      <DisplayName>小隊反応追加ランダム時間[sec]</DisplayName>
      <Description>小隊反応追加ランダム時間[sec]</Description>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>0.1</Increment>
      <SortID>13500</SortID>
    </Field>
    <Field Def="u8 searchEye_angX">
      <DisplayName>索敵視覚_角度(高さ)[deg]</DisplayName>
      <Description>索敵視覚_角度(高さ)[deg]</Description>
      <Maximum>180</Maximum>
      <SortID>11320</SortID>
    </Field>
    <Field Def="u8 isUpdateBattleSight">
      <DisplayName>戦闘視界を上書きするか？</DisplayName>
      <Enum>BOOL_DODONT_TYPE</Enum>
      <Description>戦闘視界を上書きするか</Description>
      <Maximum>1</Maximum>
      <SortID>11340</SortID>
    </Field>
    <Field Def="u16 battleEye_updateDist">
      <DisplayName>戦闘視覚_上書き距離[m]</DisplayName>
      <Description>戦闘視覚_上書き距離[m]</Description>
      <SortID>11350</SortID>
    </Field>
    <Field Def="u8 battleEye_updateAngX">
      <DisplayName>戦闘視覚_上書き角度(高さ)[deg]</DisplayName>
      <Description>戦闘視覚_上書き角度(高さ)[deg]</Description>
      <Maximum>180</Maximum>
      <SortID>11360</SortID>
    </Field>
    <Field Def="u8 battleEye_updateAngY">
      <DisplayName>戦闘視覚_上書き角度(幅)[deg]</DisplayName>
      <Description>戦闘視覚_上書き角度(幅)[deg]</Description>
      <Maximum>180</Maximum>
      <SortID>11370</SortID>
    </Field>
    <Field Def="dummy8 pad4[16]">
      <DisplayName>パディング</DisplayName>
      <SortID>15206</SortID>
    </Field>
    <Field Def="u16 eye_BackOffsetDist">
      <DisplayName>視覚_発生距離[m]</DisplayName>
      <Description>キャラの中心からこの距離後ろが視角開始位置になる</Description>
      <SortID>11200</SortID>
    </Field>
    <Field Def="u16 eye_BeginDist">
      <DisplayName>視覚_カット距離[m]</DisplayName>
      <Description>視角発生位置からこの距離は認識しない</Description>
      <Maximum>65534</Maximum>
      <SortID>11300</SortID>
    </Field>
    <Field Def="u8 actTypeOnFailedPath">
      <DisplayName>パス検索失敗/帰巣限界時の行動種別</DisplayName>
      <Enum>NPC_THINK_ACTTYPE_ON_FAILEDPATH</Enum>
      <Description>パス検索失敗時、代替パスの終点に到達した際/帰巣限界距離まで到達した際に行うデフォルトの行動種別</Description>
      <Maximum>3</Maximum>
      <SortID>10300</SortID>
    </Field>
    <Field Def="u8 goalAction_ToCautionImportant">
      <DisplayName>ゴールアクション：警戒状態/重要音</DisplayName>
      <Enum>NPC_THINK_GOAL_ACTION</Enum>
      <Description>ゴールアクション：ターゲットが重要音の感知により警戒状態になった</Description>
      <SortID>12700</SortID>
    </Field>
    <Field Def="s32 shiftAnimeId_RangedAttack = -1">
      <DisplayName>遠隔攻撃用持ち替えアニメID</DisplayName>
      <Description>AI攻撃パラメータの参照ID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>10340</SortID>
    </Field>
    <Field Def="u8 actTypeOnNonBtlFailedPath">
      <DisplayName>パス検索失敗時の挙動（非戦闘中）</DisplayName>
      <Enum>NPC_THINK_ACTTYPE_ON_NONBTL_FAILEDPATH</Enum>
      <Description>ターゲット【なし】時に、現在地点を巣に書き換えた後に取る行動 </Description>
      <Maximum>2</Maximum>
      <SortID>10350</SortID>
    </Field>
    <Field Def="u8 isBuddyAI">
      <DisplayName>バディAI</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>バディ用の思考か</Description>
      <Maximum>1</Maximum>
      <SortID>13050</SortID>
    </Field>
    <Field Def="u8 goalAction_ToSearchLv1">
      <DisplayName>ゴールアクション：索敵Lv1</DisplayName>
      <Enum>NPC_THINK_GOAL_ACTION</Enum>
      <Description>ゴールアクション：ターゲットが索敵Lv1になった</Description>
      <SortID>12530</SortID>
    </Field>
    <Field Def="u8 goalAction_ToSearchLv2">
      <DisplayName>ゴールアクション：索敵Lv2</DisplayName>
      <Enum>NPC_THINK_GOAL_ACTION</Enum>
      <Description>ゴールアクション：ターゲットが索敵Lv2になった</Description>
      <SortID>12531</SortID>
    </Field>
    <Field Def="u8 enableJumpMove">
      <DisplayName>エッジ「ジャンプ」使うか（非戦闘状態）</DisplayName>
      <Enum>NPC_THINK_JUMPUSEREDGE_USE_TYPE</Enum>
      <Description>ジャンプ用ユーザーエッジを飛び越えて移動するか(非戦闘状態)</Description>
      <Maximum>2</Maximum>
      <SortID>13550</SortID>
    </Field>
    <Field Def="u8 disableLocalSteering">
      <DisplayName>回避移動しない</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>他のキャラクターを回避しながら移動しようとする挙動(ローカルステアリング)をオフにするか？</Description>
      <Maximum>1</Maximum>
      <SortID>10200</SortID>
    </Field>
    <Field Def="u8 goalAction_ToDisappear">
      <DisplayName>ゴールアクション：記憶ターゲット状態</DisplayName>
      <Enum>NPC_THINK_GOAL_ACTION</Enum>
      <Description>ゴールアクション：ターゲットを見失った</Description>
      <SortID>12900</SortID>
    </Field>
    <Field Def="u8 changeStateAction_ToNormal">
      <DisplayName>ゴールアクション：通常状態開始</DisplayName>
      <Enum>NPC_THINK_CHANGE_STATE_ACTION</Enum>
      <Description>通常状態に遷移したときのアクション</Description>
      <SortID>13000</SortID>
    </Field>
    <Field Def="f32 MemoryTargetForgetTime = 150">
      <DisplayName>記憶ターゲット忘れる時間[sec]</DisplayName>
      <Description>記憶ターゲット忘れる時間。</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>12500</SortID>
    </Field>
    <Field Def="s32 rangedAttackId = -1">
      <DisplayName>遠隔攻撃アニメID</DisplayName>
      <Description>遠隔攻撃する際にエネミーが出す攻撃IDを指定するパラメータ</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>10320</SortID>
    </Field>
    <Field Def="u8 useFall_onNormalCaution = 2">
      <DisplayName>エッジ「飛び降り」使うか（非戦闘状態）</DisplayName>
      <Enum>NPC_THINK_JUMPUSEREDGE_USE_TYPE</Enum>
      <Description>AIが非戦闘状態で、飛び降りエッジを通行できるようにする</Description>
      <Maximum>2</Maximum>
      <SortID>13555</SortID>
    </Field>
    <Field Def="u8 useFall_onSearchBattle = 2">
      <DisplayName>エッジ「飛び降り」使うか（戦闘状態）</DisplayName>
      <Enum>NPC_THINK_JUMPUSEREDGE_USE_TYPE</Enum>
      <Description>AIが戦闘状態で、飛び降りエッジを通行できるようにする </Description>
      <Maximum>2</Maximum>
      <SortID>13556</SortID>
    </Field>
    <Field Def="u8 enableJumpMove_onBattle">
      <DisplayName>エッジ「ジャンプ」使うか（戦闘状態）</DisplayName>
      <Enum>NPC_THINK_JUMPUSEREDGE_USE_TYPE</Enum>
      <Description>ジャンプ用ユーザーエッジを飛び越えて移動するか(戦闘状態)</Description>
      <Maximum>2</Maximum>
      <SortID>13551</SortID>
    </Field>
    <Field Def="u8 backToHomeStuckAct">
      <DisplayName>帰巣限界でハマった時の挙動</DisplayName>
      <Enum>NPC_THINK_BackToHomeStuckAct</Enum>
      <Description>帰巣限界でハマった時の挙動</Description>
      <Maximum>1</Maximum>
      <SortID>10360</SortID>
    </Field>
    <Field Def="dummy8 pad3[4]">
      <DisplayName>パディング</DisplayName>
      <Description>pad</Description>
      <SortID>15207</SortID>
    </Field>
    <Field Def="s32 soundBehaviorId01 = -1">
      <DisplayName>振る舞いID1</DisplayName>
      <Description>聴くことのできる音ターゲットの振る舞いIDに対応</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13520</SortID>
    </Field>
    <Field Def="s32 soundBehaviorId02 = -1">
      <DisplayName>振る舞いID2</DisplayName>
      <Description>聴くことのできる音ターゲットの振る舞いIDに対応</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13521</SortID>
    </Field>
    <Field Def="s32 soundBehaviorId03 = -1">
      <DisplayName>振る舞いID3</DisplayName>
      <Description>聴くことのできる音ターゲットの振る舞いIDに対応</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13522</SortID>
    </Field>
    <Field Def="s32 soundBehaviorId04 = -1">
      <DisplayName>振る舞いID4</DisplayName>
      <Description>聴くことのできる音ターゲットの振る舞いIDに対応</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13523</SortID>
    </Field>
    <Field Def="s32 soundBehaviorId05 = -1">
      <DisplayName>振る舞いID5</DisplayName>
      <Description>聴くことのできる音ターゲットの振る舞いIDに対応</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13524</SortID>
    </Field>
    <Field Def="s32 soundBehaviorId06 = -1">
      <DisplayName>振る舞いID6</DisplayName>
      <Description>聴くことのできる音ターゲットの振る舞いIDに対応</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13525</SortID>
    </Field>
    <Field Def="s32 soundBehaviorId07 = -1">
      <DisplayName>振る舞いID7</DisplayName>
      <Description>聴くことのできる音ターゲットの振る舞いIDに対応</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13526</SortID>
    </Field>
    <Field Def="s32 soundBehaviorId08 = -1">
      <DisplayName>振る舞いID8</DisplayName>
      <Description>聴くことのできる音ターゲットの振る舞いIDに対応</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13527</SortID>
    </Field>
    <Field Def="s32 weaponOffSpecialEffectId = -1">
      <DisplayName>納刀時特殊効果ID</DisplayName>
      <Description>納刀時特殊効果ID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13011</SortID>
    </Field>
    <Field Def="s32 weaponOnSpecialEffectId = -1">
      <DisplayName>抜刀時特殊効果ID</DisplayName>
      <Description>抜刀時特殊効果ID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13012</SortID>
    </Field>
    <Field Def="s32 weaponOffAnimId = -1">
      <DisplayName>納刀アニメID</DisplayName>
      <Description>納刀アニメID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13013</SortID>
    </Field>
    <Field Def="s32 weaponOnAnimId = -1">
      <DisplayName>抜刀アニメID</DisplayName>
      <Description>抜刀アニメID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13014</SortID>
    </Field>
    <Field Def="s32 surpriseAnimId = -1">
      <DisplayName>驚くアニメID</DisplayName>
      <Description>驚くアニメID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>13020</SortID>
    </Field>
  </Fields>
</PARAMDEF>