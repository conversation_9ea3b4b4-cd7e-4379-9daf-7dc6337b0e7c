﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>NETWORK_MSG_PARAM_ST</ParamType>
  <DataVersion>3</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u16 priority">
      <DisplayName>優先度</DisplayName>
      <Description>優先度</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>9999</Maximum>
      <SortID>10</SortID>
    </Field>
    <Field Def="u8 forcePlay">
      <DisplayName>強制割り込み</DisplayName>
      <Enum>BOOL_YESNO_TYPE</Enum>
      <Description>強制割り込み</Description>
      <Maximum>1</Maximum>
      <SortID>20</SortID>
    </Field>
    <Field Def="dummy8 pad1[1]">
      <DisplayName>予約</DisplayName>
      <Description>予約</Description>
      <SortID>2001</SortID>
    </Field>
    <Field Def="s32 normalWhite = -1">
      <DisplayName>白霊（白サイン）</DisplayName>
      <Description>白霊（白サイン）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>110</SortID>
    </Field>
    <Field Def="s32 umbasaWhite = -1">
      <DisplayName>太陽霊（白サイン）</DisplayName>
      <Description>太陽霊（白サイン）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>120</SortID>
    </Field>
    <Field Def="s32 berserkerWhite = -1">
      <DisplayName>バーサーカー霊（白サイン）</DisplayName>
      <Description>バーサーカー霊（白サイン）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>130</SortID>
    </Field>
    <Field Def="s32 sinnerHeroWhite = -1">
      <DisplayName>罪人英雄霊（白サイン ）</DisplayName>
      <Description>罪人英雄霊（白サイン ）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>140</SortID>
    </Field>
    <Field Def="s32 normalBlack = -1">
      <DisplayName>闇霊（赤サイン）</DisplayName>
      <Description>闇霊（赤サイン）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>150</SortID>
    </Field>
    <Field Def="s32 umbasaBlack = -1">
      <DisplayName>太陽霊（赤サイン）</DisplayName>
      <Description>太陽霊（赤サイン）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>160</SortID>
    </Field>
    <Field Def="s32 berserkerBlack = -1">
      <DisplayName>バーサーカー霊（赤サイン）</DisplayName>
      <Description>バーサーカー霊（赤サイン）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>170</SortID>
    </Field>
    <Field Def="s32 forceJoinBlack = -1">
      <DisplayName>侵入_A</DisplayName>
      <Description>侵入_A</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>180</SortID>
    </Field>
    <Field Def="s32 forceJoinUmbasaBlack = -1">
      <DisplayName>太陽霊（乱入）</DisplayName>
      <Description>太陽霊（乱入）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>190</SortID>
    </Field>
    <Field Def="s32 forceJoinBerserkerBlack = -1">
      <DisplayName>バーサーカー霊（乱入）</DisplayName>
      <Description>バーサーカー霊（乱入）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="s32 sinnerHunterVisitor = -1">
      <DisplayName>罪人狩り霊（訪問）</DisplayName>
      <Description>罪人狩り霊（訪問）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>210</SortID>
    </Field>
    <Field Def="s32 redHunterVisitor = -1">
      <DisplayName>赤狩り霊（訪問）</DisplayName>
      <Description>赤狩り霊（訪問）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>220</SortID>
    </Field>
    <Field Def="s32 guardianOfBossVisitor = -1">
      <DisplayName>ボス守護霊（訪問）</DisplayName>
      <Description>ボス守護霊（訪問）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>230</SortID>
    </Field>
    <Field Def="s32 guardianOfForestMapVisitor = -1">
      <DisplayName>マップ守護霊_森（訪問）</DisplayName>
      <Description>マップ守護霊_森（訪問）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>240</SortID>
    </Field>
    <Field Def="s32 guardianOfAnolisVisitor = -1">
      <DisplayName>マップ守護霊_アノール（訪問）</DisplayName>
      <Description>マップ守護霊_アノール（訪問）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>250</SortID>
    </Field>
    <Field Def="s32 rosaliaBlack = -1">
      <DisplayName>ロザリア霊（赤サイン）</DisplayName>
      <Description>ロザリア霊（赤サイン）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>172</SortID>
    </Field>
    <Field Def="s32 forceJoinRosaliaBlack = -1">
      <DisplayName>ロザリア霊（乱入）</DisplayName>
      <Description>ロザリア霊（乱入）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>202</SortID>
    </Field>
    <Field Def="s32 redHunterVisitor2 = -1">
      <DisplayName>赤狩り霊2（訪問）</DisplayName>
      <Description>赤狩り霊2（訪問）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>222</SortID>
    </Field>
    <Field Def="s32 npc1 = -1">
      <DisplayName>NPC擬似マルチ1</DisplayName>
      <Description>NPC擬似マルチ1</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>260</SortID>
    </Field>
    <Field Def="s32 npc2 = -1">
      <DisplayName>NPC擬似マルチ2</DisplayName>
      <Description>NPC擬似マルチ2</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>270</SortID>
    </Field>
    <Field Def="s32 npc3 = -1">
      <DisplayName>NPC擬似マルチ3</DisplayName>
      <Description>NPC擬似マルチ3</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>280</SortID>
    </Field>
    <Field Def="s32 npc4 = -1">
      <DisplayName>NPC擬似マルチ4</DisplayName>
      <Description>NPC擬似マルチ4</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>290</SortID>
    </Field>
    <Field Def="s32 battleRoyal = -1">
      <DisplayName>バトルロイヤル</DisplayName>
      <Description>バトルロイヤル</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="s32 npc5 = -1">
      <DisplayName>NPC擬似マルチ5</DisplayName>
      <Description>NPC擬似マルチ5</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="s32 npc6 = -1">
      <DisplayName>NPC擬似マルチ6</DisplayName>
      <Description>NPC擬似マルチ6</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>310</SortID>
    </Field>
    <Field Def="s32 npc7 = -1">
      <DisplayName>NPC擬似マルチ7</DisplayName>
      <Description>NPC擬似マルチ7</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>320</SortID>
    </Field>
    <Field Def="s32 npc8 = -1">
      <DisplayName>NPC擬似マルチ8</DisplayName>
      <Description>NPC擬似マルチ8</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>330</SortID>
    </Field>
    <Field Def="s32 npc9 = -1">
      <DisplayName>NPC擬似マルチ9</DisplayName>
      <Description>NPC擬似マルチ9</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>340</SortID>
    </Field>
    <Field Def="s32 npc10 = -1">
      <DisplayName>NPC擬似マルチ10</DisplayName>
      <Description>NPC擬似マルチ10</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>350</SortID>
    </Field>
    <Field Def="s32 npc11 = -1">
      <DisplayName>NPC擬似マルチ11</DisplayName>
      <Description>NPC擬似マルチ11</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>360</SortID>
    </Field>
    <Field Def="s32 npc12 = -1">
      <DisplayName>NPC擬似マルチ12</DisplayName>
      <Description>NPC擬似マルチ12</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>370</SortID>
    </Field>
    <Field Def="s32 npc13 = -1">
      <DisplayName>NPC擬似マルチ13</DisplayName>
      <Description>NPC擬似マルチ13</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>380</SortID>
    </Field>
    <Field Def="s32 npc14 = -1">
      <DisplayName>NPC擬似マルチ14</DisplayName>
      <Description>NPC擬似マルチ14</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>390</SortID>
    </Field>
    <Field Def="s32 npc15 = -1">
      <DisplayName>NPC擬似マルチ15</DisplayName>
      <Description>NPC擬似マルチ15</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="s32 npc16 = -1">
      <DisplayName>NPC擬似マルチ16</DisplayName>
      <Description>NPC擬似マルチ16</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>410</SortID>
    </Field>
    <Field Def="s32 forceJoinBlack_B = -1">
      <DisplayName>侵入_B</DisplayName>
      <Description>侵入_B</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>182</SortID>
    </Field>
    <Field Def="s32 normalWhite_Npc = -1">
      <DisplayName>白霊（白サイン）_NPC用</DisplayName>
      <Description>白霊（白サイン）_NPC用</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="s32 forceJoinBlack_Npc = -1">
      <DisplayName>侵入_A_NPC用</DisplayName>
      <Description>侵入_A_NPC用</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1010</SortID>
    </Field>
    <Field Def="s32 forceJoinBlack_B_Npc = -1">
      <DisplayName>侵入_B_NPC用</DisplayName>
      <Description>侵入_B_NPC用</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1020</SortID>
    </Field>
    <Field Def="s32 forceJoinBlack_C_Npc = -1">
      <DisplayName>侵入_C_NPC用</DisplayName>
      <Description>侵入_C_NPC用</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1030</SortID>
    </Field>
    
    <Field Def="dummy8 pad2[28]" RemovedVersion="11210015" />
    
    <Field Def="s32 unknown_0xa4" FirstVersion="11210015" />
    <Field Def="s32 unknown_0xa8" FirstVersion="11210015" />
    <Field Def="s32 unknown_0xac" FirstVersion="11210015" />
    <Field Def="s32 unknown_0xb0" FirstVersion="11210015" />
    <Field Def="s32 unknown_0xb4" FirstVersion="11210015" />
    
    <Field Def="dummy8 pad2_new[8]" FirstVersion="11210015" >
      <DisplayName>予約</DisplayName>
      <Description>予約</Description>
      <SortID>2002</SortID>
    </Field>
  </Fields>
</PARAMDEF>