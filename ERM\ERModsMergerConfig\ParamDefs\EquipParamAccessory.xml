﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>EQUIP_PARAM_ACCESSORY_ST</ParamType>
  <DataVersion>2</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 disableParam_NT:1">
      <DisplayName>NT版出力から外すか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータをNT版パッケージでは除外します</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
    </Field>
    <Field Def="dummy8 disableParamReserve1:7">
      <DisplayName>パッケージ出力用リザーブ1</DisplayName>
      <Description>パッケージ出力用リザーブ1</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>13011</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve2[3]">
      <DisplayName>パッケージ出力用リザーブ2</DisplayName>
      <Description>パッケージ出力用リザーブ2</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>13012</SortID>
    </Field>
    <Field Def="s32 refId = -1">
      <DisplayName>呼び出しID</DisplayName>
      <Description>装飾品から呼び出すID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="s32 sfxVariationId = -1">
      <DisplayName>SFXバリエーションID</DisplayName>
      <Description>ＳＦＸのバリエーションを指定（TimeActEditorのＩＤと組み合わせて、ＳＦＸを特定するのに使用する）</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="f32 weight = 1">
      <DisplayName>重量[kg]</DisplayName>
      <Description>重量[kg]</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>0.1</Increment>
      <SortID>1100</SortID>
    </Field>
    <Field Def="s32 behaviorId">
      <DisplayName>行動ID</DisplayName>
      <Description>行動ID(=Skill)</Description>
      <Minimum>0</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="s32 basicPrice">
      <DisplayName>基本価格</DisplayName>
      <Description>基本価格</Description>
      <Minimum>0</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>1300</SortID>
    </Field>
    <Field Def="s32 sellValue">
      <DisplayName>売却価格</DisplayName>
      <Description>販売価格</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>1310</SortID>
    </Field>
    <Field Def="s32 sortId">
      <DisplayName>sortID</DisplayName>
      <Minimum>-1</Minimum>
      <Maximum>9999999</Maximum>
      <SortID>1600</SortID>
    </Field>
    <Field Def="s32 qwcId = -1">
      <DisplayName>QWCID</DisplayName>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>1900</SortID>
    </Field>
    <Field Def="u16 equipModelId">
      <DisplayName>装備モデル番号</DisplayName>
      <Description>装備モデルの番号</Description>
      <Maximum>9999</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="u16 iconId">
      <DisplayName>アイコンID</DisplayName>
      <Description>メニューアイコンID</Description>
      <SortID>400</SortID>
    </Field>
    <Field Def="s16 shopLv">
      <DisplayName>ショップレベル</DisplayName>
      <Description>お店で販売できるレベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1400</SortID>
    </Field>
    <Field Def="s16 trophySGradeId = -1">
      <DisplayName>トロフィー</DisplayName>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1700</SortID>
    </Field>
    <Field Def="s16 trophySeqId = -1">
      <DisplayName>トロフィーSEQ番号</DisplayName>
      <Description>トロフィーのSEQ番号</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1750</SortID>
    </Field>
    <Field Def="u8 equipModelCategory">
      <DisplayName>装備モデル種別</DisplayName>
      <Enum>EQUIP_MODEL_CATEGORY</Enum>
      <Description>装備モデルの種別</Description>
      <Maximum>99</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="u8 equipModelGender">
      <DisplayName>装備モデル性別</DisplayName>
      <Enum>EQUIP_MODEL_GENDER</Enum>
      <Description>装備モデルの性別</Description>
      <Maximum>99</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="u8 accessoryCategory">
      <DisplayName>装飾カテゴリ</DisplayName>
      <Enum>ACCESSORY_CATEGORY</Enum>
      <Description>防具のカテゴリ</Description>
      <Maximum>99</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="u8 refCategory">
      <DisplayName>IDカテゴリ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>↓のIDのカテゴリ[攻撃、飛び道具、特殊]</Description>
      <SortID>600</SortID>
    </Field>
    <Field Def="u8 spEffectCategory">
      <DisplayName>特殊効果カテゴリ</DisplayName>
      <Enum>BEHAVIOR_CATEGORY</Enum>
      <Description>スキルや、魔法、アイテムなどで、パラメータが変動する効果（エンチャントウェポンなど）があるので、│定した効果が、「武器攻撃のみをパワーアップする」といった効果に対応できるように行動ごとに設定するバリスタなど、設定の必要のないものは「なし」を設定する
</Description>
      <SortID>900</SortID>
    </Field>
    <Field Def="u8 sortGroupId = 255">
      <DisplayName>ソートアイテム種別ID</DisplayName>
      <Description>ソートアイテム種別ID。ソート「アイテム種別順」にて、同じIDは同じグループとしてまとめて表示されます</Description>
      <SortID>1610</SortID>
    </Field>
    <Field Def="s32 vagrantItemLotId">
      <DisplayName>ベイグラント時アイテム抽選ID_マップ用</DisplayName>
      <Description>-1：ベイグラントなし 0：抽選なし 1～：抽選あり</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>10000</SortID>
    </Field>
    <Field Def="s32 vagrantBonusEneDropItemLotId">
      <DisplayName>ベイグラントボーナス敵ドロップアイテム抽選ID_マップ用</DisplayName>
      <Description>-1：ドロップなし 0：抽選なし 1～：抽選あり</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>10000</SortID>
    </Field>
    <Field Def="s32 vagrantItemEneDropItemLotId">
      <DisplayName>ベイグラントアイテム敵ドロップアイテム抽選ID_マップ用</DisplayName>
      <Description>-1：ドロップなし 0：抽選なし 1～：抽選あり</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>10000</SortID>
    </Field>
    <Field Def="u8 isDeposit:1">
      <DisplayName>預けれるか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>倉庫へ預けれるか</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>1800</SortID>
    </Field>
    <Field Def="u8 isEquipOutBrake:1">
      <DisplayName>外すと壊れるか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>装備して外す時に壊れるか</Description>
      <Maximum>1</Maximum>
      <SortID>1810</SortID>
    </Field>
    <Field Def="u8 disableMultiDropShare:1">
      <DisplayName>マルチドロップ共有禁止か</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>マルチドロップ共有禁止か</Description>
      <Maximum>1</Maximum>
      <SortID>1805</SortID>
    </Field>
    <Field Def="u8 isDiscard:1">
      <DisplayName>捨てれるか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>アイテムを捨てれるか？TRUE=捨てれる</Description>
      <Maximum>1</Maximum>
      <SortID>1780</SortID>
    </Field>
    <Field Def="u8 isDrop:1">
      <DisplayName>その場に置けるか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>アイテムをその場に置けるか？TRUE=置ける</Description>
      <Maximum>1</Maximum>
      <SortID>1790</SortID>
    </Field>
    <Field Def="u8 showLogCondType:1 = 1">
      <DisplayName>取得ログ表示条件</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>アイテム取得時にアイテム取得ログに表示するか（未入力: ○）</Description>
      <Maximum>1</Maximum>
      <SortID>13000</SortID>
    </Field>
    <Field Def="u8 showDialogCondType:2 = 2">
      <DisplayName>取得ダイアログ表示条件</DisplayName>
      <Enum>GET_DIALOG_CONDITION_TYPE</Enum>
      <Description>アイテム取得時にアイテム取得ダイアログに表示するか（未入力: newのみ）</Description>
      <Maximum>2</Maximum>
      <SortID>12900</SortID>
    </Field>
    <Field Def="u8 rarity">
      <DisplayName>レア度</DisplayName>
      <Description>アイテム取得ログで使うレア度</Description>
      <Maximum>99</Maximum>
      <SortID>13010</SortID>
    </Field>
    <Field Def="dummy8 pad2[2]">
      <DisplayName>pad</DisplayName>
      <Description>（旧ログ用アイコンID）</Description>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>13013</SortID>
    </Field>
    <Field Def="s32 saleValue = -1">
      <DisplayName>販売価格</DisplayName>
      <Description>販売価格</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>1320</SortID>
    </Field>
    <Field Def="s16 accessoryGroup = -1">
      <DisplayName>装着グループID</DisplayName>
      <Description>同じグループの物は同時装備不可能</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>11000</SortID>
    </Field>
    <Field Def="dummy8 pad3[1]">
      <DisplayName>pad</DisplayName>
      <Description>pad</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>13014</SortID>
    </Field>
    <Field Def="s8 compTrophySedId = -1">
      <DisplayName>コンプトロフィーSEQ番号</DisplayName>
      <Description>コンプリート系トロフィのSEQ番号</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>1725</SortID>
    </Field>
    <Field Def="s32 residentSpEffectId1">
      <DisplayName>常駐特殊効果ID1</DisplayName>
      <Description>常駐特殊効果ID1</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>710</SortID>
    </Field>
    <Field Def="s32 residentSpEffectId2">
      <DisplayName>常駐特殊効果ID2</DisplayName>
      <Description>常駐特殊効果ID2</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>720</SortID>
    </Field>
    <Field Def="s32 residentSpEffectId3">
      <DisplayName>常駐特殊効果ID3</DisplayName>
      <Description>常駐特殊効果ID3</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>730</SortID>
    </Field>
    <Field Def="s32 residentSpEffectId4">
      <DisplayName>常駐特殊効果ID4</DisplayName>
      <Description>常駐特殊効果ID4</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>740</SortID>
    </Field>
    <Field Def="dummy8 pad1[4]">
      <DisplayName>pad</DisplayName>
      <Description>pad</Description>
      <SortID>13015</SortID>
    </Field>
  </Fields>
</PARAMDEF>