﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>GRAPHICS_COMMON_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 hitBulletDecalOffset_HitIns = 0.05">
      <DisplayName>HIT INSに弾丸が当たった時のデカール発生位置オフセット</DisplayName>
      <Description>HIT INSに当たった時に発生するデカールの発生位置を法線方向にこの値だけオフセットする</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.1</Increment>
      <SortID>100</SortID>
    </Field>
    <Field Def="dummy8 reserved02[8]">
      <DisplayName>予約</DisplayName>
      <Description>(dummy8)</Description>
      <SortID>401</SortID>
    </Field>
    <Field Def="f32 charaWetDecalFadeRange = 0.6">
      <DisplayName>キャラが濡れた時のデカールフェード範囲[m]</DisplayName>
      <Description>キャラが濡れた時にデカールを消すフェード範囲[m]</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="dummy8 reserved04[240]">
      <DisplayName>予約</DisplayName>
      <Description>(dummy8)</Description>
      <SortID>402</SortID>
    </Field>
  </Fields>
</PARAMDEF>