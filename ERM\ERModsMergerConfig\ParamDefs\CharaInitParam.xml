﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>CHARACTER_INIT_PARAM</ParamType>
  <DataVersion>2</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 baseRec_mp">
      <DisplayName>ＭＰ回復速度基本値[s]</DisplayName>
      <Description>ＭＰが、1ポイント回復するまでの時間（小数点第一位）</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>1100</SortID>
    </Field>
    <Field Def="f32 baseRec_sp">
      <DisplayName>スタミナ回復速度基本値[s]</DisplayName>
      <Description>スタミナが、1ポイント回復するまでの時間（小数点第一位）</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>1300</SortID>
    </Field>
    <Field Def="f32 red_Falldam">
      <DisplayName>落下ダメージ軽減補正[%]</DisplayName>
      <Description>他のキャラクターに上からのしかかれたときに、クッションとなりえるダメージ軽減量（％）（小数点第一位）</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>1400</SortID>
    </Field>
    <Field Def="s32 soul">
      <DisplayName>初期ソウル</DisplayName>
      <Description>初期に所持しているソウル量</Description>
      <Minimum>0</Minimum>
      <Maximum>10000000</Maximum>
      <SortID>1500</SortID>
    </Field>
    <Field Def="s32 equip_Wep_Right = -1">
      <DisplayName>右手武器スロット1</DisplayName>
      <Description>装備品パラメータの武器ＩＤ(右手スロット１)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1610</SortID>
    </Field>
    <Field Def="s32 equip_Subwep_Right = -1">
      <DisplayName>右手武器スロット2</DisplayName>
      <Description>装備品パラメータの武器ＩＤ(右手スロット２)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1630</SortID>
    </Field>
    <Field Def="s32 equip_Wep_Left = -1">
      <DisplayName>左手武器スロット1</DisplayName>
      <Description>装備品パラメータの武器ＩＤ(左手スロット１)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1710</SortID>
    </Field>
    <Field Def="s32 equip_Subwep_Left = -1">
      <DisplayName>左手武器スロット2</DisplayName>
      <Description>装備品パラメータの武器ＩＤ(左手スロット２)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1730</SortID>
    </Field>
    <Field Def="s32 equip_Helm = -1">
      <DisplayName>頭防具</DisplayName>
      <Description>装備品パラメータの防具ＩＤ(頭防具)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="s32 equip_Armer = -1">
      <DisplayName>胴体防具</DisplayName>
      <Description>装備品パラメータの防具ＩＤ(胴体防具)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="s32 equip_Gaunt = -1">
      <DisplayName>腕防具</DisplayName>
      <Description>装備品パラメータの防具ＩＤ(腕防具)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2200</SortID>
    </Field>
    <Field Def="s32 equip_Leg = -1">
      <DisplayName>脚防具</DisplayName>
      <Description>装備品パラメータの防具ＩＤ(脚防具)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2300</SortID>
    </Field>
    <Field Def="s32 equip_Arrow = -1">
      <DisplayName>矢</DisplayName>
      <Description>装備品パラメータの武器ＩＤ(矢)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2400</SortID>
    </Field>
    <Field Def="s32 equip_Bolt = -1">
      <DisplayName>ボルト</DisplayName>
      <Description>装備品パラメータの武器ＩＤ(ボルト)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2600</SortID>
    </Field>
    <Field Def="s32 equip_SubArrow = -1">
      <DisplayName>予備矢</DisplayName>
      <Description>装備品パラメータの武器ＩＤ(矢予備)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2500</SortID>
    </Field>
    <Field Def="s32 equip_SubBolt = -1">
      <DisplayName>予備ボルト</DisplayName>
      <Description>装備品パラメータの武器ＩＤ(ボルト予備)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2700</SortID>
    </Field>
    <Field Def="s32 equip_Accessory01 = -1">
      <DisplayName>装飾品1</DisplayName>
      <Description>装備品パラメータの装飾品ＩＤ01</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2800</SortID>
    </Field>
    <Field Def="s32 equip_Accessory02 = -1">
      <DisplayName>装飾品2</DisplayName>
      <Description>装備品パラメータの装飾品ＩＤ02</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2900</SortID>
    </Field>
    <Field Def="s32 equip_Accessory03 = -1">
      <DisplayName>装飾品3</DisplayName>
      <Description>装備品パラメータの装飾品ＩＤ03</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3000</SortID>
    </Field>
    <Field Def="s32 equip_Accessory04 = -1">
      <DisplayName>装飾品4</DisplayName>
      <Description>装備品パラメータの装飾品ＩＤ04</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3100</SortID>
    </Field>
    
    <Field Def="dummy8 pad8_old[4]" RemovedVersion="11210015" />
    
    <Field Def="u8 unknown_0x50" FirstVersion="11210015" />
    <Field Def="u8 unknown_0x51" FirstVersion="11210015" />
    <Field Def="dummy8 pad8[2]" FirstVersion="11210015" >
      <DisplayName>pad</DisplayName>
      <Description>pad</Description>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>10001</SortID>
    </Field>
    
    <Field Def="s32 elixir_material00 = -1">
      <DisplayName>エリクサー用素材ID1</DisplayName>
      <Description>エリクサー用素材ID1</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6580</SortID>
    </Field>
    <Field Def="s32 elixir_material01 = -1">
      <DisplayName>エリクサー用素材ID2</DisplayName>
      <Description>エリクサー用素材ID2</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6581</SortID>
    </Field>
    <Field Def="s32 elixir_material02 = -1">
      <DisplayName>エリクサー用素材ID3</DisplayName>
      <Description>エリクサー用素材ID3</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6582</SortID>
    </Field>
    <Field Def="s32 equip_Spell_01 = -1">
      <DisplayName>魔法・奇跡1</DisplayName>
      <Description>初期配置の魔法・奇跡ID01</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3600</SortID>
    </Field>
    <Field Def="s32 equip_Spell_02 = -1">
      <DisplayName>魔法・奇跡2</DisplayName>
      <Description>初期配置の魔法・奇跡ID02</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3700</SortID>
    </Field>
    <Field Def="s32 equip_Spell_03 = -1">
      <DisplayName>魔法・奇跡3</DisplayName>
      <Description>初期配置の魔法・奇跡ID03</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3800</SortID>
    </Field>
    <Field Def="s32 equip_Spell_04 = -1">
      <DisplayName>魔法・奇跡4</DisplayName>
      <Description>初期配置の魔法・奇跡ID04</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3900</SortID>
    </Field>
    <Field Def="s32 equip_Spell_05 = -1">
      <DisplayName>魔法・奇跡5</DisplayName>
      <Description>初期配置の魔法・奇跡ID05</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>4000</SortID>
    </Field>
    <Field Def="s32 equip_Spell_06 = -1">
      <DisplayName>魔法・奇跡6</DisplayName>
      <Description>初期配置の魔法・奇跡ID06</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>4100</SortID>
    </Field>
    <Field Def="s32 equip_Spell_07 = -1">
      <DisplayName>魔法・奇跡7</DisplayName>
      <Description>初期配置の魔法・奇跡ID07</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>4200</SortID>
    </Field>
    <Field Def="s32 item_01 = -1">
      <DisplayName>アイテム01</DisplayName>
      <Description>初期所持のアイテムID01</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>4600</SortID>
    </Field>
    <Field Def="s32 item_02 = -1">
      <DisplayName>アイテム02</DisplayName>
      <Description>初期所持のアイテムID02</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>4800</SortID>
    </Field>
    <Field Def="s32 item_03 = -1">
      <DisplayName>アイテム03</DisplayName>
      <Description>初期所持のアイテムID03</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>5000</SortID>
    </Field>
    <Field Def="s32 item_04 = -1">
      <DisplayName>アイテム04</DisplayName>
      <Description>初期所持のアイテムID04</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>5200</SortID>
    </Field>
    <Field Def="s32 item_05 = -1">
      <DisplayName>アイテム05</DisplayName>
      <Description>初期所持のアイテムID05</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>5400</SortID>
    </Field>
    <Field Def="s32 item_06 = -1">
      <DisplayName>アイテム06</DisplayName>
      <Description>初期所持のアイテムID06</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>5600</SortID>
    </Field>
    <Field Def="s32 item_07 = -1">
      <DisplayName>アイテム07</DisplayName>
      <Description>初期所持のアイテムID07</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>5800</SortID>
    </Field>
    <Field Def="s32 item_08 = -1">
      <DisplayName>アイテム08</DisplayName>
      <Description>初期所持のアイテムID08</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6000</SortID>
    </Field>
    <Field Def="s32 item_09 = -1">
      <DisplayName>アイテム09</DisplayName>
      <Description>初期所持のアイテムID09</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6200</SortID>
    </Field>
    <Field Def="s32 item_10 = -1">
      <DisplayName>アイテム10</DisplayName>
      <Description>初期所持のアイテムID10</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6400</SortID>
    </Field>
    <Field Def="s32 npcPlayerFaceGenId">
      <DisplayName>フェイスジェンパラメータID</DisplayName>
      <Description>NPCプレイヤーで使用するフェイスジェンパラメータID。通常プレイヤーでは使用しません。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>6600</SortID>
    </Field>
    <Field Def="s32 npcPlayerThinkId">
      <DisplayName>NPCプレイヤーの思考ID</DisplayName>
      <Description>NPCプレイヤーで使用するNPC思考パラメータID。通常プレイヤーでは使用しません。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>6700</SortID>
    </Field>
    <Field Def="u16 baseHp">
      <DisplayName>ＨＰ基本値</DisplayName>
      <Description>ＨＰの基本値（実際は、計算式で補正される）</Description>
      <Maximum>999</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="u16 baseMp">
      <DisplayName>ＭＰ基本値</DisplayName>
      <Description>ＭＰの基本値（実際は、計算式で補正される）</Description>
      <Maximum>999</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="u16 baseSp">
      <DisplayName>スタミナ基本値</DisplayName>
      <Description>スタミナの基本値（実際は、計算式で補正される）</Description>
      <Maximum>999</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="u16 arrowNum">
      <DisplayName>矢の所持数</DisplayName>
      <Description>矢の初期所持数</Description>
      <Maximum>999</Maximum>
      <SortID>2450</SortID>
    </Field>
    <Field Def="u16 boltNum">
      <DisplayName>ボルトの所持数</DisplayName>
      <Description>ボルトの初期所持数</Description>
      <Maximum>999</Maximum>
      <SortID>2650</SortID>
    </Field>
    <Field Def="u16 subArrowNum">
      <DisplayName>予備矢の所持数</DisplayName>
      <Description>矢の初期所持数</Description>
      <Maximum>999</Maximum>
      <SortID>2550</SortID>
    </Field>
    <Field Def="u16 subBoltNum">
      <DisplayName>予備ボルトの所持数</DisplayName>
      <Description>ボルトの初期所持数</Description>
      <Maximum>999</Maximum>
      <SortID>2750</SortID>
    </Field>
    <Field Def="dummy8 pad4[6]">
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>10002</SortID>
    </Field>
    <Field Def="s16 soulLv">
      <DisplayName>ソウルLv</DisplayName>
      <Description>初期Lv</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>10</SortID>
    </Field>
    <Field Def="u8 baseVit">
      <DisplayName>体力</DisplayName>
      <Description>体力の基本値</Description>
      <Maximum>99</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="u8 baseWil">
      <DisplayName>精神</DisplayName>
      <Description>精神の基本値</Description>
      <Maximum>99</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="u8 baseEnd">
      <DisplayName>頑強</DisplayName>
      <Description>頑強の基本値</Description>
      <Maximum>99</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="u8 baseStr">
      <DisplayName>筋力</DisplayName>
      <Description>筋力の基本値</Description>
      <Maximum>99</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="u8 baseDex">
      <DisplayName>俊敏</DisplayName>
      <Description>俊敏の基本値</Description>
      <Maximum>99</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="u8 baseMag">
      <DisplayName>魔力</DisplayName>
      <Description>魔力の基本値</Description>
      <Maximum>99</Maximum>
      <SortID>600</SortID>
    </Field>
    <Field Def="u8 baseFai">
      <DisplayName>信仰</DisplayName>
      <Description>信仰の基本値</Description>
      <Maximum>99</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="u8 baseLuc">
      <DisplayName>運</DisplayName>
      <Description>運の基本値</Description>
      <Maximum>99</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="u8 baseHeroPoint">
      <DisplayName>人間性</DisplayName>
      <Description>人間性の基本値</Description>
      <Maximum>99</Maximum>
      <SortID>810</SortID>
    </Field>
    <Field Def="u8 baseDurability">
      <DisplayName>耐久力</DisplayName>
      <Description>耐久力の基本値</Description>
      <Maximum>99</Maximum>
      <SortID>820</SortID>
    </Field>
    <Field Def="u8 itemNum_01">
      <DisplayName>アイテム01の所持数</DisplayName>
      <Description>初期所持のアイテム個数01</Description>
      <Maximum>99</Maximum>
      <SortID>4700</SortID>
    </Field>
    <Field Def="u8 itemNum_02">
      <DisplayName>アイテム02の所持数</DisplayName>
      <Description>初期所持のアイテム個数02</Description>
      <Maximum>99</Maximum>
      <SortID>4900</SortID>
    </Field>
    <Field Def="u8 itemNum_03">
      <DisplayName>アイテム03の所持数</DisplayName>
      <Description>初期所持のアイテム個数03</Description>
      <Maximum>99</Maximum>
      <SortID>5100</SortID>
    </Field>
    <Field Def="u8 itemNum_04">
      <DisplayName>アイテム個数04</DisplayName>
      <Description>初期所持のアイテム個数04</Description>
      <Maximum>99</Maximum>
      <SortID>5300</SortID>
    </Field>
    <Field Def="u8 itemNum_05">
      <DisplayName>アイテム個数05</DisplayName>
      <Description>初期所持のアイテム個数05</Description>
      <Maximum>99</Maximum>
      <SortID>5500</SortID>
    </Field>
    <Field Def="u8 itemNum_06">
      <DisplayName>アイテム個数06</DisplayName>
      <Description>初期所持のアイテム個数06</Description>
      <Maximum>99</Maximum>
      <SortID>5700</SortID>
    </Field>
    <Field Def="u8 itemNum_07">
      <DisplayName>アイテム個数07</DisplayName>
      <Description>初期所持のアイテム個数07</Description>
      <Maximum>99</Maximum>
      <SortID>5900</SortID>
    </Field>
    <Field Def="u8 itemNum_08">
      <DisplayName>アイテム個数08</DisplayName>
      <Description>初期所持のアイテム個数08</Description>
      <Maximum>99</Maximum>
      <SortID>6100</SortID>
    </Field>
    <Field Def="u8 itemNum_09">
      <DisplayName>アイテム個数09</DisplayName>
      <Description>初期所持のアイテム個数09</Description>
      <Maximum>99</Maximum>
      <SortID>6300</SortID>
    </Field>
    <Field Def="u8 itemNum_10">
      <DisplayName>アイテム個数10</DisplayName>
      <Description>初期所持のアイテム個数10</Description>
      <Maximum>99</Maximum>
      <SortID>6500</SortID>
    </Field>
    <Field Def="dummy8 pad5[5]">
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>10003</SortID>
    </Field>
    <Field Def="s8 gestureId0 = -1">
      <DisplayName>ジェスチャーID0</DisplayName>
      <Description>ジェスチャー0番目(EzStateのジェスチャー0を再生したいなら0)</Description>
      <Minimum>-1</Minimum>
      <SortID>8600</SortID>
    </Field>
    <Field Def="s8 gestureId1 = -1">
      <DisplayName>ジェスチャーID1</DisplayName>
      <Description>ジェスチャー1番目(EzStateのジェスチャー0を再生したいなら0)</Description>
      <Minimum>-1</Minimum>
      <SortID>8700</SortID>
    </Field>
    <Field Def="s8 gestureId2 = -1">
      <DisplayName>ジェスチャーID2</DisplayName>
      <Description>ジェスチャー2番目(EzStateのジェスチャー0を再生したいなら0)</Description>
      <Minimum>-1</Minimum>
      <SortID>8800</SortID>
    </Field>
    <Field Def="s8 gestureId3 = -1">
      <DisplayName>ジェスチャーID3</DisplayName>
      <Description>ジェスチャー3番目(EzStateのジェスチャー0を再生したいなら0)</Description>
      <Minimum>-1</Minimum>
      <SortID>8900</SortID>
    </Field>
    <Field Def="s8 gestureId4 = -1">
      <DisplayName>ジェスチャーID4</DisplayName>
      <Description>ジェスチャー4番目(EzStateのジェスチャー0を再生したいなら0)</Description>
      <Minimum>-1</Minimum>
      <SortID>9000</SortID>
    </Field>
    <Field Def="s8 gestureId5 = -1">
      <DisplayName>ジェスチャーID5</DisplayName>
      <Description>ジェスチャー5番目(EzStateのジェスチャー0を再生したいなら0)</Description>
      <Minimum>-1</Minimum>
      <SortID>9100</SortID>
    </Field>
    <Field Def="s8 gestureId6 = -1">
      <DisplayName>ジェスチャーID6</DisplayName>
      <Description>ジェスチャー6番目(EzStateのジェスチャー0を再生したいなら0)</Description>
      <Minimum>-1</Minimum>
      <SortID>9200</SortID>
    </Field>
    <Field Def="u8 npcPlayerType">
      <DisplayName>NPCプレイヤーのNPCタイプ</DisplayName>
      <Enum>NPC_TYPE</Enum>
      <Description>NPCプレイヤーで使用するNPCタイプ。通常プレイヤーでは使用しません。</Description>
      <SortID>6800</SortID>
    </Field>
    <Field Def="s8 npcPlayerDrawType">
      <DisplayName>NPCプレイヤーの描画タイプ</DisplayName>
      <Enum>NPC_DRAW_TYPE</Enum>
      <Description>NPCプレイヤーで使用する描画タイプ。通常プレイヤーでは使用しません。</Description>
      <Minimum>0</Minimum>
      <Maximum>255</Maximum>
      <SortID>6900</SortID>
    </Field>
    <Field Def="u8 npcPlayerSex">
      <DisplayName>NPCプレイヤーの性別</DisplayName>
      <Enum>CHARACTER_INIT_SEX</Enum>
      <Description>NPCプレイヤーで使用する性別です。通常プレイヤーには反映しません。</Description>
      <Maximum>1</Maximum>
      <SortID>7000</SortID>
    </Field>
    <Field Def="u8 vowType:4">
      <DisplayName>誓約</DisplayName>
      <Enum>VOW_TYPE</Enum>
      <Description>誓約タイプ(なし：0)</Description>
      <Maximum>15</Maximum>
      <SortID>8500</SortID>
    </Field>
    <Field Def="u8 isSyncTarget:1">
      <DisplayName>送受信対象か</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>送受信対象か（コピーNPC用）</Description>
      <Maximum>1</Maximum>
      <SortID>10000</SortID>
    </Field>
    <Field Def="dummy8 pad:3">
      <SortID>10004</SortID>
    </Field>
    <Field Def="dummy8 pad6[2]">
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>10005</SortID>
    </Field>
    <Field Def="u8 wepParamType_Right1">
      <DisplayName>右手武器スロット1装備タイプ</DisplayName>
      <Enum>CHARA_INIT_WEP_TYPE</Enum>
      <Description>右手武器スロット１のパラメータ参照先</Description>
      <Maximum>1</Maximum>
      <SortID>1600</SortID>
    </Field>
    <Field Def="u8 wepParamType_Right2">
      <DisplayName>右手武器スロット2装備タイプ</DisplayName>
      <Enum>CHARA_INIT_WEP_TYPE</Enum>
      <Description>右手武器スロット２のパラメータ参照先</Description>
      <Maximum>1</Maximum>
      <SortID>1620</SortID>
    </Field>
    <Field Def="u8 wepParamType_Right3">
      <DisplayName>右手武器スロット3装備タイプ</DisplayName>
      <Enum>CHARA_INIT_WEP_TYPE</Enum>
      <Description>右手武器スロット３のパラメータ参照先</Description>
      <Maximum>1</Maximum>
      <SortID>1640</SortID>
    </Field>
    <Field Def="u8 wepParamType_Left1">
      <DisplayName>左手武器スロット1装備タイプ</DisplayName>
      <Enum>CHARA_INIT_WEP_TYPE</Enum>
      <Description>左手武器スロット１のパラメータ参照先</Description>
      <Maximum>1</Maximum>
      <SortID>1700</SortID>
    </Field>
    <Field Def="u8 wepParamType_Left2">
      <DisplayName>左手武器スロット2装備タイプ</DisplayName>
      <Enum>CHARA_INIT_WEP_TYPE</Enum>
      <Description>左手武器スロット２のパラメータ参照先</Description>
      <Maximum>1</Maximum>
      <SortID>1720</SortID>
    </Field>
    <Field Def="u8 wepParamType_Left3">
      <DisplayName>左手武器スロット3装備タイプ</DisplayName>
      <Enum>CHARA_INIT_WEP_TYPE</Enum>
      <Description>左手武器スロット３のパラメータ参照先</Description>
      <Maximum>1</Maximum>
      <SortID>1740</SortID>
    </Field>
    <Field Def="dummy8 pad2[26]">
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>10006</SortID>
    </Field>
    <Field Def="s32 equip_Subwep_Right3 = -1">
      <DisplayName>右手武器スロット3</DisplayName>
      <Description>装備品パラメータの武器ＩＤ(右手スロット３)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1650</SortID>
    </Field>
    <Field Def="s32 equip_Subwep_Left3 = -1">
      <DisplayName>左手武器スロット3</DisplayName>
      <Description>装備品パラメータの武器ＩＤ(左手スロット３)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1750</SortID>
    </Field>
    <Field Def="dummy8 pad3[4]">
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>10007</SortID>
    </Field>
    <Field Def="s32 secondaryItem_01 = -1">
      <DisplayName>第二アイテム01</DisplayName>
      <Description>第二ショートカット初期所持のアイテムID01</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6510</SortID>
    </Field>
    <Field Def="s32 secondaryItem_02 = -1">
      <DisplayName>第二アイテム02</DisplayName>
      <Description>第二ショートカット初期所持のアイテムID02</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6520</SortID>
    </Field>
    <Field Def="s32 secondaryItem_03 = -1">
      <DisplayName>第二アイテム03</DisplayName>
      <Description>第二ショートカット初期所持のアイテムID03</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6530</SortID>
    </Field>
    <Field Def="s32 secondaryItem_04 = -1">
      <DisplayName>第二アイテム04</DisplayName>
      <Description>第二ショートカット初期所持のアイテムID04</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6540</SortID>
    </Field>
    <Field Def="s32 secondaryItem_05 = -1">
      <DisplayName>第二アイテム05</DisplayName>
      <Description>第二ショートカット初期所持のアイテムID05</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6550</SortID>
    </Field>
    <Field Def="s32 secondaryItem_06 = -1">
      <DisplayName>第二アイテム06</DisplayName>
      <Description>第二ショートカット初期所持のアイテムID06</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6560</SortID>
    </Field>
    <Field Def="u8 secondaryItemNum_01">
      <DisplayName>第二アイテム01の所持数</DisplayName>
      <Description>第二ショートカット初期所持のアイテム個数01</Description>
      <Maximum>99</Maximum>
      <SortID>6511</SortID>
    </Field>
    <Field Def="u8 secondaryItemNum_02">
      <DisplayName>第二アイテム02の所持数</DisplayName>
      <Description>第二ショートカット初期所持のアイテム個数02</Description>
      <Maximum>99</Maximum>
      <SortID>6521</SortID>
    </Field>
    <Field Def="u8 secondaryItemNum_03">
      <DisplayName>第二アイテム03の所持数</DisplayName>
      <Description>第二ショートカット初期所持のアイテム個数03</Description>
      <Maximum>99</Maximum>
      <SortID>6531</SortID>
    </Field>
    <Field Def="u8 secondaryItemNum_04">
      <DisplayName>第二アイテム04の所持数</DisplayName>
      <Description>第二ショートカット初期所持のアイテム個数04</Description>
      <Maximum>99</Maximum>
      <SortID>6541</SortID>
    </Field>
    <Field Def="u8 secondaryItemNum_05">
      <DisplayName>第二アイテム05の所持数</DisplayName>
      <Description>第二ショートカット初期所持のアイテム個数05</Description>
      <Maximum>99</Maximum>
      <SortID>6551</SortID>
    </Field>
    <Field Def="u8 secondaryItemNum_06">
      <DisplayName>第二アイテム06の所持数</DisplayName>
      <Description>第二ショートカット初期所持のアイテム個数06</Description>
      <Maximum>99</Maximum>
      <SortID>6561</SortID>
    </Field>
    <Field Def="s8 HpEstMax = -1">
      <DisplayName>HPエスト瓶 所持限界数</DisplayName>
      <Description>HPエスト瓶 所持限界数</Description>
      <Minimum>-1</Minimum>
      <Maximum>20</Maximum>
      <SortID>7010</SortID>
    </Field>
    <Field Def="s8 MpEstMax = -1">
      <DisplayName>MPエスト瓶 所持限界数</DisplayName>
      <Description>MPエスト瓶 所持限界数</Description>
      <Minimum>-1</Minimum>
      <Maximum>20</Maximum>
      <SortID>7011</SortID>
    </Field>
    <Field Def="dummy8 pad7[5]">
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>10008</SortID>
    </Field>
    <Field Def="u8 voiceType">
      <DisplayName>声タイプ</DisplayName>
      <Description>声タイプ</Description>
      <SortID>7001</SortID>
    </Field>
    <Field Def="dummy8 reserve[6]">
      <DisplayName>予約領域</DisplayName>
      <Description>予約領域</Description>
      <SortID>10009</SortID>
    </Field>
  </Fields>
</PARAMDEF>