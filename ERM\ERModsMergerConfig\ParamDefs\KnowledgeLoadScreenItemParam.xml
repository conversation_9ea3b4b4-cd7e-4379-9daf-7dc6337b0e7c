﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>KNOWLEDGE_LOADSCREEN_ITEM_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 disableParam_NT:1">
      <DisplayName>NT版出力から外すか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータをNT版パッケージでは除外します</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
    </Field>
    <Field Def="dummy8 disableParamReserve1:7">
      <DisplayName>パッケージ出力用リザーブ1</DisplayName>
      <Description>パッケージ出力用リザーブ1</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>301</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve2[3]">
      <DisplayName>パッケージ出力用リザーブ2</DisplayName>
      <Description>パッケージ出力用リザーブ2</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>302</SortID>
    </Field>
    <Field Def="u32 unlockFlagId">
      <DisplayName>解禁フラグ</DisplayName>
      <Description>解禁フラグ(default: 0)。このイベントフラグが立っていれば解禁される（ローディング画面に表示される）。0なら常に解禁されている。無効フラグの方が優先される</Description>
      <Maximum>999999999</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="u32 invalidFlagId">
      <DisplayName>無効フラグ</DisplayName>
      <Description>無効フラグ(default: 0)。このイベントフラグが立っていると無効化（ローディング画面に表示されなくなる）。0なら常にこのフラグは立っていないものとする</Description>
      <Maximum>999999999</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="s32 msgId">
      <DisplayName>テキストID</DisplayName>
      <Description>テキストID(Loading Text.xlsx)。ローディングタイトルとローディングテキスト両方に使われる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>100</SortID>
    </Field>
  </Fields>
</PARAMDEF>