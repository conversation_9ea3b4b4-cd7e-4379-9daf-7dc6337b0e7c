﻿<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>DECAL_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="s32 textureId = -1">
      <DisplayName>テクスチャID</DisplayName>
      <Description>テクスチャID</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="s32 dmypolyId = -1">
      <DisplayName>ダミポリID</DisplayName>
      <Description>デカール発生基準のダミポリID。TAEで指定している場合はTAEの値になる</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>201</SortID>
    </Field>
    <Field Def="f32 pitchAngle">
      <DisplayName>基準角度オフセット_上下[deg]</DisplayName>
      <Description>基準角度オフセット_上下[deg]</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <Increment>1</Increment>
      <SortID>300</SortID>
    </Field>
    <Field Def="f32 yawAngle">
      <DisplayName>基準角度オフセット_左右[deg]</DisplayName>
      <Description>基準角度オフセット_左右[deg]</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <Increment>1</Increment>
      <SortID>301</SortID>
    </Field>
    <Field Def="f32 nearDistance">
      <DisplayName>貼り付け開始距離[m]</DisplayName>
      <Description>貼り付け開始距離[m]</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.1</Increment>
      <SortID>401</SortID>
    </Field>
    <Field Def="f32 farDistance">
      <DisplayName>貼り付け終了距離[m]</DisplayName>
      <Description>貼り付け終了距離[m]</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.1</Increment>
      <SortID>402</SortID>
    </Field>
    <Field Def="f32 nearSize">
      <DisplayName>開始距離での大きさ[m]</DisplayName>
      <Description>開始距離での大きさ[m]</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.1</Increment>
      <SortID>403</SortID>
    </Field>
    <Field Def="f32 farSize">
      <DisplayName>終了距離での大きさ[m]</DisplayName>
      <Description>終了距離での大きさ[m]</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.1</Increment>
      <SortID>404</SortID>
    </Field>
    <Field Def="s32 maskSpeffectId = -1">
      <DisplayName>監視特殊効果ID</DisplayName>
      <Description>監視特殊効果ID。任意の特殊効果IDを入れた場合、その特殊効果がかかっていないとデカールを発生しなくなる。</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="u32 pad_10:4">
      <DisplayName>パディング</DisplayName>
      <Description>パディング</Description>
      <EditFlags>None</EditFlags>
      <Maximum>9</Maximum>
      <SortID>9999</SortID>
    </Field>
    <Field Def="u32 replaceTextureId_byMaterial:1">
      <DisplayName>材質によるテクスチャ差し替え</DisplayName>
      <Description>攻撃のヒットで発生させるときに1で防御材質によってテクスチャを変える。新しいテクスチャID=血材質ID*10000000+元のテクスチャID</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>160</SortID>
    </Field>
    <Field Def="u32 dmypolyCategory:2">
      <DisplayName>ダミポリ検索場所</DisplayName>
      <Description>ダミポリ検索場所 0:本体 1:左手武器 2:右手武器</Description>
      <EditFlags>None</EditFlags>
      <Maximum>2</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="u32 pad_05:4">
      <DisplayName>パディング</DisplayName>
      <Description>パディング</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>9999</SortID>
    </Field>
    <Field Def="u32 useDeferredDecal:1 = 1">
      <DisplayName>デファード</DisplayName>
      <Description>1でデファードデカールとして機能する</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>600</SortID>
    </Field>
    <Field Def="u32 usePaintDecal:1 = 1">
      <DisplayName>ペイント</DisplayName>
      <Description>1でペイントデカールとして機能する</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>601</SortID>
    </Field>
    <Field Def="u32 bloodTypeEnable:1">
      <DisplayName>流血表現</DisplayName>
      <Description>オプションの流血表現の影響を受けるか、マイルドでIDが+1000される、非表示だと貼り付けない</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>603</SortID>
    </Field>
    <Field Def="u32 bUseNormal:1">
      <DisplayName>ノーマル成分を使用するか</DisplayName>
      <Description>ノーマル成分を使用するなら1（ノーマルとシャイニネスのテクスチャ統合対応）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>604</SortID>
    </Field>
    <Field Def="u32 pad_08:1">
      <DisplayName>パディング</DisplayName>
      <Description>パディング</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>9999</SortID>
    </Field>
    <Field Def="u32 pad_09:1">
      <DisplayName>パディング</DisplayName>
      <Description>パディング</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>9999</SortID>
    </Field>
    <Field Def="u32 usePom:1">
      <DisplayName>POMを有効にするか</DisplayName>
      <Description>POMを有効にするか</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="u32 useEmissive:1">
      <DisplayName>エミッシブを更新するか</DisplayName>
      <Description>エミッシブを更新するか</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="u32 putVertical:1">
      <DisplayName>垂直に貼り付けるか</DisplayName>
      <Description>垂直に貼り付けるか</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="s16 randomSizeMin = 100">
      <DisplayName>ランダムスケール最小値[％]</DisplayName>
      <Description>ランダムスケール最小値[％]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1010</SortID>
    </Field>
    <Field Def="s16 randomSizeMax = 100">
      <DisplayName>ランダムスケール最大値[％]</DisplayName>
      <Description>ランダムスケール最大値[％]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1011</SortID>
    </Field>
    <Field Def="f32 randomRollMin">
      <DisplayName>ランダム角度_ひねり最小値[deg]</DisplayName>
      <Description>ランダム角度_ひねり最小値[deg]</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <Increment>1</Increment>
      <SortID>1020</SortID>
    </Field>
    <Field Def="f32 randomRollMax">
      <DisplayName>ランダム角度_ひねり最大値[deg]</DisplayName>
      <Description>ランダム角度_ひねり最大値[deg]</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <Increment>1</Increment>
      <SortID>1021</SortID>
    </Field>
    <Field Def="f32 randomPitchMin">
      <DisplayName>ランダム角度_上下最小値[deg]</DisplayName>
      <Description>ランダム角度_上下最小値[deg]</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <Increment>1</Increment>
      <SortID>1030</SortID>
    </Field>
    <Field Def="f32 randomPitchMax">
      <DisplayName>ランダム角度_上下最大値[deg]</DisplayName>
      <Description>ランダム角度_上下最大値[deg]</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <Increment>1</Increment>
      <SortID>1031</SortID>
    </Field>
    <Field Def="f32 randomYawMin">
      <DisplayName>ランダム角度_左右最小値[deg]</DisplayName>
      <Description>ランダム角度_左右最小値[deg]</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <Increment>1</Increment>
      <SortID>1040</SortID>
    </Field>
    <Field Def="f32 randomYawMax">
      <DisplayName>ランダム角度_左右最大値[deg]</DisplayName>
      <Description>ランダム角度_左右最大値[deg]</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <Increment>1</Increment>
      <SortID>1041</SortID>
    </Field>
    <Field Def="f32 pomHightScale = 1">
      <DisplayName>POM高さスケール</DisplayName>
      <Description>POM高さスケール</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <Increment>0.001</Increment>
      <SortID>710</SortID>
    </Field>
    <Field Def="u8 pomSampleMin = 8">
      <DisplayName>POM最小サンプル数</DisplayName>
      <Description>POM最小サンプル数</Description>
      <EditFlags>None</EditFlags>
      <Minimum>8</Minimum>
      <Maximum>128</Maximum>
      <SortID>720</SortID>
    </Field>
    <Field Def="u8 pomSampleMax = 64">
      <DisplayName>POM最大サンプル数</DisplayName>
      <Description>POM最大サンプル数</Description>
      <EditFlags>None</EditFlags>
      <Minimum>8</Minimum>
      <Maximum>128</Maximum>
      <SortID>730</SortID>
    </Field>
    <Field Def="s8 blendMode = 1">
      <DisplayName>ブレンドモード</DisplayName>
      <Enum>DECAL_PARAM_BLEND_MODE</Enum>
      <Description>ブレンドモード</Description>
      <Minimum>1</Minimum>
      <Maximum>2</Maximum>
      <SortID>602</SortID>
    </Field>
    <Field Def="s8 appearDirType">
      <DisplayName>デカールを飛ばす基準座標</DisplayName>
      <Enum>DECAL_PARAM_DIR_TYPE</Enum>
      <Description>デカールを飛ばす方向を決定する基準座標</Description>
      <Minimum>0</Minimum>
      <Maximum>3</Maximum>
      <SortID>302</SortID>
    </Field>
    <Field Def="f32 emissiveValueBegin = 1">
      <DisplayName>エミッシブ 開始値</DisplayName>
      <Description>エミッシブ 開始値</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>801</SortID>
    </Field>
    <Field Def="f32 emissiveValueEnd = 1">
      <DisplayName>エミッシブ 終了値</DisplayName>
      <Description>エミッシブ 終了値</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>802</SortID>
    </Field>
    <Field Def="f32 emissiveTime">
      <DisplayName>エミッシブ 更新時間(秒)</DisplayName>
      <Description>開始値～終了値の補間時間</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>803</SortID>
    </Field>
    <Field Def="u8 bIntpEnable">
      <DisplayName>補間するか？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>TAEのデカル発生でバーを伸ばしてる時間発生させるか？</Description>
      <Maximum>1</Maximum>
      <SortID>5200</SortID>
    </Field>
    <Field Def="dummy8 pad_01[3]">
      <DisplayName>パディング</DisplayName>
      <Description>パディング</Description>
      <SortID>10000</SortID>
    </Field>
    <Field Def="f32 intpIntervalDist = 0.1">
      <DisplayName>補間間隔[m]</DisplayName>
      <Description>補間有効時にTAEのバーの間で発生したデカルを補間する距離</Description>
      <Minimum>0.01</Minimum>
      <Maximum>99</Maximum>
      <SortID>5201</SortID>
    </Field>
    <Field Def="s32 beginIntpTextureId = -1">
      <DisplayName>補間開始時のテクスチャID</DisplayName>
      <Description>補間開始時のテクスチャID（-1でテクスチャIDと同じ値を使う）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>5202</SortID>
    </Field>
    <Field Def="s32 endIntpTextureId = -1">
      <DisplayName>補間終了時のテクスチャID</DisplayName>
      <Description>補間終了時のテクスチャテクスチャID（-1でテクスチャIDと同じ値を使う）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>5203</SortID>
    </Field>
    <Field Def="s32 appearSfxId = -1">
      <DisplayName>デカールが貼られた時に出すSFXID</DisplayName>
      <Description>デカールが貼られた時に出すSFXID（-1で何も出さない）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>5300</SortID>
    </Field>
    <Field Def="f32 appearSfxOffsetPos">
      <DisplayName>SFXのオフセット位置</DisplayName>
      <Description>SFX発生位置のオフセット距離</Description>
      <DisplayFormat>%d</DisplayFormat>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <SortID>5301</SortID>
    </Field>
    <Field Def="s32 maskTextureId = -1">
      <DisplayName>マスクテクスチャID</DisplayName>
      <Description>マスクテクスチャID（-1でテクスチャIDを見る）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>101</SortID>
    </Field>
    <Field Def="s32 diffuseTextureId = -1">
      <DisplayName>アルベドテクスチャID</DisplayName>
      <Description>アルベドテクスチャID（-1でテクスチャIDを見る）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>102</SortID>
    </Field>
    <Field Def="s32 reflecTextureId = -1">
      <DisplayName>リフレクテクスチャID</DisplayName>
      <Description>リフレクタンステクスチャID（-1でテクスチャIDを見る）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>103</SortID>
    </Field>
    <Field Def="f32 maskScale = 1">
      <DisplayName>マスクの強さ</DisplayName>
      <Description>マスクの強さ（現状、デファードデカールでのみ有効）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>185</SortID>
    </Field>
    <Field Def="s32 normalTextureId = -1">
      <DisplayName>ノーマルテクスチャID</DisplayName>
      <Description>ノーマルテクスチャID（-1でテクスチャIDを見る）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>105</SortID>
    </Field>
    <Field Def="s32 heightTextureId = -1">
      <DisplayName>ハイトテクスチャID</DisplayName>
      <Description>ハイトテクスチャID（-1でテクスチャIDを見る）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>106</SortID>
    </Field>
    <Field Def="s32 emissiveTextureId = -1">
      <DisplayName>エミッシブテクスチャID</DisplayName>
      <Description>エミッシブテクスチャID（-1でテクスチャIDを見る）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>107</SortID>
    </Field>
    <Field Def="u8 diffuseColorR = 255">
      <DisplayName>アルベドカラー：R</DisplayName>
      <Description>アルベドの色：R</Description>
      <EditFlags>None</EditFlags>
      <SortID>170</SortID>
    </Field>
    <Field Def="u8 diffuseColorG = 255">
      <DisplayName>アルベドカラー：G</DisplayName>
      <Description>アルベドの色：G</Description>
      <EditFlags>None</EditFlags>
      <SortID>171</SortID>
    </Field>
    <Field Def="u8 diffuseColorB = 255">
      <DisplayName>アルベドカラー：B</DisplayName>
      <Description>アルベドの色：B</Description>
      <EditFlags>None</EditFlags>
      <SortID>172</SortID>
    </Field>
    <Field Def="dummy8 pad_03[1]">
      <DisplayName>パディング</DisplayName>
      <Description>パディング</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>10001</SortID>
    </Field>
    <Field Def="u8 reflecColorR = 255">
      <DisplayName>リフレクカラー：R</DisplayName>
      <Description>リフレクの色：R</Description>
      <EditFlags>None</EditFlags>
      <SortID>175</SortID>
    </Field>
    <Field Def="u8 reflecColorG = 255">
      <DisplayName>リフレクカラー：G</DisplayName>
      <Description>リフレクの色：G</Description>
      <EditFlags>None</EditFlags>
      <SortID>176</SortID>
    </Field>
    <Field Def="u8 reflecColorB = 255">
      <DisplayName>リフレクカラー：B</DisplayName>
      <Description>リフレクの色：B</Description>
      <EditFlags>None</EditFlags>
      <SortID>177</SortID>
    </Field>
    <Field Def="u8 bLifeEnable">
      <DisplayName>寿命が有効か</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>寿命が有効か</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>850</SortID>
    </Field>
    <Field Def="f32 siniScale = 1">
      <DisplayName>シャイニネスの強さ</DisplayName>
      <Description>シャイニネスの強さ</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>178</SortID>
    </Field>
    <Field Def="f32 lifeTimeSec">
      <DisplayName>寿命[秒]</DisplayName>
      <Description>寿命[秒]（デカールが貼られてからの時間、フェードイン時間は関係ない）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>851</SortID>
    </Field>
    <Field Def="f32 fadeOutTimeSec">
      <DisplayName>フェードアウト時間[秒]</DisplayName>
      <Description>フェードアウト時間[秒]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>853</SortID>
    </Field>
    <Field Def="s16 priority = -1">
      <DisplayName>優先度</DisplayName>
      <Description>この値が大きいほど残りやすい（-1は消滅しない）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="u8 bDistThinOutEnable">
      <DisplayName>近くにデカールがあれば間引くか</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>近くにデカールがあれば間引くかどうか</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>910</SortID>
    </Field>
    <Field Def="u8 bAlignedTexRandomVariationEnable">
      <DisplayName>ランダムパターンを固定化する</DisplayName>
      <Enum>BOOL_YESNO_TYPE</Enum>
      <Description>「はい」にすると、各バリエーション数が0以外のテクスチャについてランダムに決めた一つのバリエーション番号が適用されるようになります。0以外の各バリエーション数は同じ値に揃える必要があります。</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>140</SortID>
    </Field>
    <Field Def="f32 distThinOutCheckDist">
      <DisplayName>この距離以内なら間引き候補</DisplayName>
      <Description>この距離以内にデカールがあれば間引き候補</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>921</SortID>
    </Field>
    <Field Def="f32 distThinOutCheckAngleDeg">
      <DisplayName>方向の差がこの角度[度]以内なら間引き候補</DisplayName>
      <Description>デカールの方向の差がこの角度以内なら間引き候補</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>180</Maximum>
      <Increment>0.1</Increment>
      <SortID>922</SortID>
    </Field>
    <Field Def="u8 distThinOutMaxNum = 1">
      <DisplayName>条件を満たした数がこの数以上なら間引く</DisplayName>
      <Description>距離、角度がこの数以上なら間引く</Description>
      <EditFlags>None</EditFlags>
      <Minimum>1</Minimum>
      <SortID>923</SortID>
    </Field>
    <Field Def="u8 distThinOutCheckNum = 1">
      <DisplayName>直近何個まで間引きチェックするか</DisplayName>
      <Description>間引き候補を直近何個まで調べるか</Description>
      <EditFlags>None</EditFlags>
      <Minimum>1</Minimum>
      <SortID>924</SortID>
    </Field>
    <Field Def="s16 delayAppearFrame">
      <DisplayName>発生するまでの遅延フレーム[フレーム（30FPS換算）]</DisplayName>
      <Description>デカールを貼り付けようとしてからこのフレーム後に実際に貼り付けられる</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>860</SortID>
    </Field>
    <Field Def="u32 randVaria_Diffuse:4">
      <DisplayName>アルベド・バリエーション数</DisplayName>
      <Description>アルベドテクスチャのランダムバリエーション数（0番目を含む、2でテクスチャ2枚分）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>9</Maximum>
      <SortID>150</SortID>
    </Field>
    <Field Def="u32 randVaria_Mask:4">
      <DisplayName>マスク・バリエーション数</DisplayName>
      <Description>マスクテクスチャのランダムバリエーション数（0番目を含む、2でテクスチャ2枚分）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>9</Maximum>
      <SortID>151</SortID>
    </Field>
    <Field Def="u32 randVaria_Reflec:4">
      <DisplayName>リフレク・バリエーション数</DisplayName>
      <Description>リフレクテクスチャのランダムバリエーション数（0番目を含む、2でテクスチャ2枚分）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>9</Maximum>
      <SortID>152</SortID>
    </Field>
    <Field Def="u32 pad_12:4">
      <DisplayName>パディング</DisplayName>
      <EditFlags>None</EditFlags>
      <Maximum>9</Maximum>
      <SortID>9999</SortID>
    </Field>
    <Field Def="u32 randVaria_Normal:4">
      <DisplayName>ノーマル・バリエーション数</DisplayName>
      <Description>ノーマルテクスチャのランダムバリエーション数（0番目を含む、2でテクスチャ2枚分）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>9</Maximum>
      <SortID>154</SortID>
    </Field>
    <Field Def="u32 randVaria_Height:4">
      <DisplayName>ハイト・バリエーション数</DisplayName>
      <Description>ハイトテクスチャのランダムバリエーション数（0番目を含む、2でテクスチャ2枚分）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>9</Maximum>
      <SortID>155</SortID>
    </Field>
    <Field Def="u32 randVaria_Emissive:4">
      <DisplayName>エミッシブ・バリエーション数</DisplayName>
      <Description>エミッシブテクスチャのランダムバリエーション数（0番目を含む、2でテクスチャ2枚分）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>9</Maximum>
      <SortID>156</SortID>
    </Field>
    <Field Def="u32 pad_11:4">
      <DisplayName>パディング</DisplayName>
      <Description>パディング</Description>
      <EditFlags>None</EditFlags>
      <Maximum>9</Maximum>
      <SortID>9999</SortID>
    </Field>
    <Field Def="f32 fadeInTimeSec">
      <DisplayName>フェードイン時間[秒]</DisplayName>
      <Description>フェードイン時間[秒]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>852</SortID>
    </Field>
    <Field Def="f32 thinOutOverlapMultiRadius">
      <DisplayName>間引き:重複乗算値</DisplayName>
      <Description>デカールサイズにこの値を乗算した範囲で重複かを判定する</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <SortID>912</SortID>
    </Field>
    <Field Def="f32 thinOutNeighborAddRadius">
      <DisplayName>間引き:近隣加算距離[m]</DisplayName>
      <Description>デカールサイズにこの距離[m]を加算した範囲で近隣かを判定する</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-999</Minimum>
      <Maximum>999</Maximum>
      <SortID>913</SortID>
    </Field>
    <Field Def="u32 thinOutOverlapLimitNum">
      <DisplayName>間引き:重複限界数</DisplayName>
      <Description>重複可能な限界数</Description>
      <EditFlags>None</EditFlags>
      <Maximum>9999</Maximum>
      <SortID>914</SortID>
    </Field>
    <Field Def="u32 thinOutNeighborLimitNum">
      <DisplayName>間引き:近隣限界数</DisplayName>
      <Description>近隣可能な限界数</Description>
      <EditFlags>None</EditFlags>
      <Maximum>9999</Maximum>
      <SortID>915</SortID>
    </Field>
    <Field Def="s8 thinOutMode">
      <DisplayName>間引きモード</DisplayName>
      <Enum>DECAL_PARAM_THIN_OUT_MODE</Enum>
      <Description>間引きモード</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>911</SortID>
    </Field>
    <Field Def="u8 emissiveColorR = 255">
      <DisplayName>エミッシブカラー：R</DisplayName>
      <Description>エミッシブの色：R</Description>
      <EditFlags>None</EditFlags>
      <SortID>179</SortID>
    </Field>
    <Field Def="u8 emissiveColorG = 255">
      <DisplayName>エミッシブカラー：G</DisplayName>
      <Description>エミッシブの色：G</Description>
      <EditFlags>None</EditFlags>
      <SortID>180</SortID>
    </Field>
    <Field Def="u8 emissiveColorB = 255">
      <DisplayName>エミッシブカラー：B</DisplayName>
      <Description>エミッシブの色：B</Description>
      <EditFlags>None</EditFlags>
      <SortID>181</SortID>
    </Field>
    <Field Def="f32 maxDecalSfxCreatableSlopeAngleDeg = -1">
      <DisplayName>SFX発生上限角度</DisplayName>
      <Description>SFX発生上限角度</Description>
      <EditFlags>Lock</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>360</Maximum>
      <Increment>1</Increment>
      <SortID>5302</SortID>
    </Field>
    <Field Def="dummy8 pad_02[40]">
      <DisplayName>パディング</DisplayName>
      <Description>パディング</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>10002</SortID>
    </Field>
  </Fields>
</PARAMDEF>